/**
 * This script configures Hermes JavaScript engine for the app.
 * <PERSON><PERSON> improves app start-up time, reduces memory usage, and decreases app size.
 * 
 * Run with: npm run hermes
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// Paths to configuration files
const appJsonPath = path.join(__dirname, '../app.json');
const buildGradlePath = path.join(__dirname, '../android/app/build.gradle');
const podfilePath = path.join(__dirname, '../ios/Podfile');

// Enable Hermes in app.json
function enableHermesInAppJson() {
  try {
    const appJsonContent = fs.readFileSync(appJsonPath, 'utf8');
    const appJson = JSON.parse(appJsonContent);
    
    // Check current Hermes status
    const hermesEnabled = appJson.expo?.jsEngine === 'hermes';
    
    if (hermesEnabled) {
      console.log('✓ Hermes is already enabled in app.json');
      return;
    }
    
    // Update the configuration
    if (!appJson.expo) {
      appJson.expo = {};
    }
    
    appJson.expo.jsEngine = 'hermes';
    
    // Write back to the file
    fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2));
    console.log('✓ Enabled Hermes in app.json');
  } catch (error) {
    console.error('Error updating app.json:', error);
  }
}

// Verify Android build.gradle configuration
function checkAndroidConfig() {
  try {
    if (!fs.existsSync(buildGradlePath)) {
      console.log('⚠️ Android build.gradle not found. Run "npx expo prebuild" first.');
      return;
    }
    
    const buildGradleContent = fs.readFileSync(buildGradlePath, 'utf8');
    
    // Check if Hermes is enabled
    if (buildGradleContent.includes('enableHermes: true')) {
      console.log('✓ Hermes is already enabled in Android build.gradle');
    } else {
      console.log('⚠️ Hermes may not be enabled in Android build.gradle.');
      console.log('   Consider running "npx expo prebuild" to regenerate native files.');
    }
  } catch (error) {
    console.error('Error checking Android config:', error);
  }
}

// Verify iOS Podfile configuration
function checkIosConfig() {
  try {
    if (!fs.existsSync(podfilePath)) {
      console.log('⚠️ iOS Podfile not found. Run "npx expo prebuild" first.');
      return;
    }
    
    const podfileContent = fs.readFileSync(podfilePath, 'utf8');
    
    // Check if Hermes is enabled
    if (podfileContent.includes(':hermes_enabled => true')) {
      console.log('✓ Hermes is already enabled in iOS Podfile');
    } else {
      console.log('⚠️ Hermes may not be enabled in iOS Podfile.');
      console.log('   Consider running "npx expo prebuild" to regenerate native files.');
    }
  } catch (error) {
    console.error('Error checking iOS config:', error);
  }
}

// Run the configuration
function configureHermes() {
  console.log('=== Configuring Hermes JavaScript Engine ===');
  
  // Enable Hermes in configuration files
  enableHermesInAppJson();
  checkAndroidConfig();
  checkIosConfig();
  
  console.log('\n=== Next Steps ===');
  console.log('1. Run "npx expo prebuild" to regenerate native files');
  console.log('2. Run "npm run android" or "npm run ios" to build with Hermes');
  console.log('\n=== Benefits of Hermes ===');
  console.log('• Faster app startup');
  console.log('• Reduced memory usage');
  console.log('• Smaller app size');
  console.log('• Better performance on low-end devices');
}

// Execute the script
configureHermes(); 