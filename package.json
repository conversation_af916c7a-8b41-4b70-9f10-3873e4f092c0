{"name": "billing_app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "build:android": "eas build -p android", "build:ios": "eas build -p ios", "clean": "rm -rf node_modules && npm cache clean --force && npm install", "clean:metro": "rm -rf $TMPDIR/metro-* && rm -rf $TMPDIR/haste-map-*", "hermes": "node ./scripts/useHermes.js", "test": "jest"}, "dependencies": {"@babel/plugin-proposal-decorators": "^7.27.1", "@expo/vector-icons": "^14.1.0", "@nozbe/watermelondb": "^0.28.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-community/netinfo": "^11.2.1", "@react-native-community/slider": "^4.5.6", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/drawer": "^7.3.12", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "date-fns": "^4.1.0", "expo": "~53.0.9", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-dev-client": "~5.1.8", "expo-device": "^7.1.4", "expo-document-picker": "^13.1.5", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-image-manipulator": "^13.1.7", "expo-linear-gradient": "^14.1.4", "expo-linking": "~7.1.5", "expo-notifications": "~0.31.2", "expo-print": "^14.1.4", "expo-router": "~5.0.6", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "lodash.isequal": "^4.5.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-bluetooth-escpos-printer": "^0.0.5", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.4", "react-native-paper-dates": "^0.22.42", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "^15.12.0", "react-native-tcp-socket": "^6.3.0", "react-native-thermal-printer": "^2.3.6", "react-native-usb-serialport": "^3.0.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "react-native-zeroconf": "^0.13.8"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "jest": "^29.2.1", "jest-expo": "~49.0.0", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "typescript": "~5.8.3"}, "private": true, "jest": {"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}}