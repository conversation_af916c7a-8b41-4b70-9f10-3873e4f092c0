# Icon Replacement Guide

To use your custom icon for the Gift Corner app, you'll need to place your icon image in the following locations:

## 1. Main App Icon
Save your icon as: `assets/images/icon.png`

This icon should be at least 1024×1024 pixels for best results on all platforms.

## 2. Android Adaptive Icon
Save your icon as: `assets/images/adaptive-icon.png`

Android adaptive icons require a foreground image with transparent padding around the main content. The foreground should be centered with about 33% padding around it.

## 3. Splash Screen (Optional)
If you want to update the splash screen too, save your icon as: `assets/images/splash.png`

The splash screen image should be a PNG image with transparent background, ideally 1242×2436 pixels.

## 4. Web Favicon (Optional)
For the web version, save a smaller version as: `assets/images/favicon.png`

## After Replacing Icons

After replacing the icon files, you'll need to rebuild your app for the changes to take effect:

```bash
npx expo prebuild --clean
npx expo run:android  # For Android
npx expo run:ios      # For iOS (requires macOS)
```

## Generating Icons From a Single Source

If you want to generate all the required icons from a single source image, you can use tools like:
- [EAS Build](https://docs.expo.dev/build/setup/) with automatic icon generation
- [Expo Image Generator](https://github.com/expo/expo-cli/tree/master/packages/expo-image-utils)
- Online tools like [App Icon Generator](https://appicon.co/) 