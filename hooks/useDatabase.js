import { useEffect, useState, useCallback, useMemo } from 'react'
import { database, billsCollection, customersCollection, itemsCollection, paymentsCollection } from '../database'
import { Q } from '@nozbe/watermelondb'

export function useDatabase() {
  const [isReady, setIsReady] = useState(false)
  
  useEffect(() => {
    const initDatabase = async () => {
      try {
        await database.write(async () => {
          // You can add initial data here if needed
        })
        setIsReady(true)
      } catch (error) {
        console.error('Failed to initialize database:', error)
      }
    }
    
    initDatabase()
    
    return () => {
      // Clean up if needed
    }
  }, [])
  
  return {
    database,
    isReady,
    collections: {
      bills: billsCollection,
      customers: customersCollection,
      items: itemsCollection,
      payments: paymentsCollection
    }
  }
}

// Hook to observe a collection with optional query builder
export function useCollection(collection, queryBuilder = null) {
  const [data, setData] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  
  // Memoize the query to prevent unnecessary rebuilding
  const query = useMemo(() => {
    if (queryBuilder) {
      return queryBuilder(collection)
    }
    return collection.query()
  }, [collection, queryBuilder])
  
  useEffect(() => {
    setIsLoading(true)
    
    const subscription = query.observe().subscribe(results => {
      setData(results)
      setIsLoading(false)
    })
    
    return () => subscription.unsubscribe()
  }, [query])
  
  return { data, isLoading }
}

// Hook to observe a collection with pagination
export function usePaginatedCollection(collection, queryBuilder = null, pageSize = 20) {
  const [data, setData] = useState([])
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)
  const [isLoading, setIsLoading] = useState(true)
  const [totalCount, setTotalCount] = useState(0)
  
  // Memoize the query to prevent unnecessary rebuilding
  const query = useMemo(() => {
    if (queryBuilder) {
      return queryBuilder(collection)
    }
    return collection.query()
  }, [collection, queryBuilder])
  
  // Load initial data
  useEffect(() => {
    setIsLoading(true)
    setPage(1)
    setData([])
    setHasMore(true)
    
    // First, get the total count
    const loadTotalCount = async () => {
      try {
        const allResults = await query.fetch()
        setTotalCount(allResults.length)
      } catch (error) {
        console.error('Error fetching total count:', error)
      }
    }
    
    loadTotalCount()
    
    // Then load the first page
    const loadInitialData = async () => {
      try {
        const results = await query.extend(Q.take(pageSize)).fetch()
        
        setData(results)
        setHasMore(results.length === pageSize)
        setIsLoading(false)
      } catch (error) {
        console.error('Error loading paginated data:', error)
        setIsLoading(false)
      }
    }
    
    loadInitialData()
  }, [query, pageSize])
  
  // Function to load the next page
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoading) return
    
    setIsLoading(true)
    
    try {
      const nextPage = page + 1
      const skipCount = (nextPage - 1) * pageSize
      
      const results = await query
        .extend(Q.skip(skipCount), Q.take(pageSize))
        .fetch()
      
      if (results.length > 0) {
        setData(prevData => [...prevData, ...results])
        setPage(nextPage)
        setHasMore(results.length === pageSize)
      } else {
        setHasMore(false)
      }
    } catch (error) {
      console.error('Error loading more data:', error)
    } finally {
      setIsLoading(false)
    }
  }, [query, page, pageSize, hasMore, isLoading])
  
  return { 
    data, 
    isLoading, 
    hasMore, 
    loadMore,
    page,
    totalCount,
    totalPages: Math.ceil(totalCount / pageSize)
  }
}

// Hook to observe a single record with all relations preloaded
export function useRecordWithRelations(collection, id, relations = []) {
  const [record, setRecord] = useState(null)
  const [relatedData, setRelatedData] = useState({})
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  
  useEffect(() => {
    if (!id) {
      setIsLoading(false)
      return
    }
    
    let subscription
    
    const fetchRecord = async () => {
      try {
        // First observe the main record
        subscription = collection.findAndObserve(id).subscribe(async result => {
          setRecord(result)
          
          // If relations are specified, load them
          if (relations.length > 0) {
            const relatedResults = {}
            
            // Load all relations in parallel
            await Promise.all(relations.map(async relation => {
              try {
                const data = await result[relation].fetch()
                relatedResults[relation] = data
              } catch (err) {
                console.error(`Error fetching relation ${relation}:`, err)
              }
            }))
            
            setRelatedData(relatedResults)
          }
          
          setIsLoading(false)
        })
      } catch (err) {
        console.error('Error finding record:', err)
        setError(err)
        setIsLoading(false)
      }
    }
    
    fetchRecord()
    
    return () => subscription?.unsubscribe()
  }, [collection, id, relations])
  
  return { record, relatedData, isLoading, error }
}

// Hook to observe a single record
export function useRecord(collection, id) {
  const [record, setRecord] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  
  useEffect(() => {
    if (!id) {
      setIsLoading(false)
      return
    }
    
    const subscription = collection.findAndObserve(id).subscribe(result => {
      setRecord(result)
      setIsLoading(false)
    })
    
    return () => subscription.unsubscribe()
  }, [collection, id])
  
  return { record, isLoading }
} 