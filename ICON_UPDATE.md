# Gift Corner App Icon Update Guide

This guide explains how to update the icons for your Gift Corner app on Android.

## Prerequisites

Before you begin, make sure you have the following icon files ready:

1. **adaptive-icon.png** - This is used for the foreground of Android's adaptive icons
   - Should be 1024x1024px
   - Should have a transparent background
   - Foreground content (like the gift bag logo) should be within the safe area (central 2/3 of the image)

2. **icon.png** - This is the main app icon
   - Should be 1024x1024px
   - Should have either a transparent background or a solid color background

3. **favicon.png** - This is used for web
   - Should be 196x196px

## Update Steps

### Method 1: Using the Interactive Setup Script (Recommended)

1. Place your icon files (`adaptive-icon.png`, `icon.png`, and `favicon.png`) in a location on your computer.

2. Run the interactive setup script from the project root directory:
   ```
   node setup_icons.js
   ```

3. Follow the prompts to specify the location of your icon files.

4. The script will automatically:
   - Copy your icons to the appropriate directories
   - Update the Android configuration files
   - Set the background color to match the Gift Corner theme

5. Rebuild your app to see the changes:
   ```
   npm run android
   ```

### Method 2: Manual Setup

If you prefer to set up the icons manually:

1. Place your icon files in the project root directory:
   - `adaptive-icon.png`
   - `icon.png`
   - `favicon.png`

2. Run the non-interactive script:
   ```
   node temp_icons/process_icons.js
   ```

3. Rebuild your app:
   ```
   npm run android
   ```

## iOS Icon Update

This guide focuses on Android icon updates. For iOS, you will need to:

1. Navigate to `ios/GiftCorner/Images.xcassets/AppIcon.appiconset`
2. Replace the icon files with your own (following Apple's size requirements)
3. Rebuild the iOS app

## Troubleshooting

If you encounter any issues:

1. **Problem**: Icons don't appear after rebuilding
   **Solution**: Try cleaning the build and rebuilding:
   ```
   cd android && ./gradlew clean && cd .. && npm run android
   ```

2. **Problem**: Wrong icon sizes
   **Solution**: The script does not resize your icons. Make sure your input icons are the correct size.

3. **Problem**: Adaptive icon looks wrong
   **Solution**: Make sure your adaptive icon has a transparent background and the content is centered.

For any other issues, please check the React Native and Expo documentation for icon requirements. 