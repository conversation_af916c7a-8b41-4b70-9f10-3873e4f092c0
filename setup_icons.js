const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const rootDir = process.cwd();
const androidResDir = path.join(rootDir, 'android', 'app', 'src', 'main', 'res');

console.log('=== Gift Corner Icon Setup ===\n');
console.log('This script will help you set up your app icons for Gift Corner.\n');

// Check if we're in the right directory
if (!fs.existsSync(path.join(rootDir, 'package.json'))) {
  console.error('Error: This script must be run from the root of your React Native project');
  process.exit(1);
}

// Function to check if a file exists
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (err) {
    return false;
  }
}

// Ask for icon locations
console.log('Please provide the locations of your icon files:\n');

rl.question('Path to adaptive-icon.png (or press Enter to use default ./adaptive-icon.png): ', (adaptiveIconPath) => {
  adaptiveIconPath = adaptiveIconPath || './adaptive-icon.png';
  
  rl.question('Path to icon.png (or press Enter to use default ./icon.png): ', (iconPath) => {
    iconPath = iconPath || './icon.png';
    
    rl.question('Path to favicon.png (or press Enter to use default ./favicon.png): ', (faviconPath) => {
      faviconPath = faviconPath || './favicon.png';
      rl.close();
      
      // Check if files exist
      const adaptiveIconExists = fileExists(path.resolve(adaptiveIconPath));
      const iconExists = fileExists(path.resolve(iconPath));
      const faviconExists = fileExists(path.resolve(faviconPath));
      
      if (!adaptiveIconExists) {
        console.error(`Error: Adaptive icon file not found at ${adaptiveIconPath}`);
        process.exit(1);
      }
      
      if (!iconExists) {
        console.error(`Error: Icon file not found at ${iconPath}`);
        process.exit(1);
      }
      
      if (!faviconExists) {
        console.error(`Error: Favicon file not found at ${faviconPath}`);
        process.exit(1);
      }
      
      console.log('\nAll icon files found! Proceeding with installation...\n');
      
      try {
        // Define densities
        const densities = ['mdpi', 'hdpi', 'xhdpi', 'xxhdpi', 'xxxhdpi'];
        
        // Create necessary directories
        densities.forEach(density => {
          const dir = path.join(androidResDir, `mipmap-${density}`);
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
          }
        });
        
        // Ensure anydpi-v26 directory exists
        const anydpiDir = path.join(androidResDir, 'mipmap-anydpi-v26');
        if (!fs.existsSync(anydpiDir)) {
          fs.mkdirSync(anydpiDir, { recursive: true });
        }
        
        // Copy icon files directly
        console.log('Copying icon files to appropriate directories...');
        
        // Copy adaptive icon to all densities
        densities.forEach(density => {
          const targetDir = path.join(androidResDir, `mipmap-${density}`);
          
          // Copy the regular icon
          fs.copyFileSync(
            path.resolve(iconPath),
            path.join(targetDir, 'ic_launcher.png')
          );
          console.log(`✓ Copied icon to mipmap-${density}/ic_launcher.png`);
          
          // Copy the round icon (using the same source)
          fs.copyFileSync(
            path.resolve(iconPath),
            path.join(targetDir, 'ic_launcher_round.png')
          );
          console.log(`✓ Copied icon to mipmap-${density}/ic_launcher_round.png`);
          
          // Copy the adaptive icon foreground
          fs.copyFileSync(
            path.resolve(adaptiveIconPath),
            path.join(targetDir, 'ic_launcher_foreground.png')
          );
          console.log(`✓ Copied adaptive icon to mipmap-${density}/ic_launcher_foreground.png`);
        });
        
        // Update the adaptive icon XML files
        console.log('\nUpdating adaptive icon configuration...');
        
        // ic_launcher.xml
        fs.writeFileSync(
          path.join(anydpiDir, 'ic_launcher.xml'),
          `<?xml version="1.0" encoding="utf-8"?>
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
    <background android:drawable="@color/iconBackground"/>
    <foreground android:drawable="@mipmap/ic_launcher_foreground"/>
</adaptive-icon>
`
        );
        console.log('✓ Updated ic_launcher.xml');
        
        // ic_launcher_round.xml
        fs.writeFileSync(
          path.join(anydpiDir, 'ic_launcher_round.xml'),
          `<?xml version="1.0" encoding="utf-8"?>
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
    <background android:drawable="@color/iconBackground"/>
    <foreground android:drawable="@mipmap/ic_launcher_foreground"/>
</adaptive-icon>
`
        );
        console.log('✓ Updated ic_launcher_round.xml');
        
        // Update the colors.xml file to match the icon's theme
        console.log('\nUpdating icon background color...');
        const colorsPath = path.join(androidResDir, 'values', 'colors.xml');
        
        if (fs.existsSync(colorsPath)) {
          let colorsContent = fs.readFileSync(colorsPath, 'utf8');
          
          // Update the iconBackground color - using a teal color that matches the Gift Corner theme
          colorsContent = colorsContent.replace(
            /<color name="iconBackground">#[a-fA-F0-9]{6}<\/color>/,
            '<color name="iconBackground">#42A5B2</color>'
          );
          
          fs.writeFileSync(colorsPath, colorsContent);
          console.log('✓ Updated colors.xml with theme color #42A5B2');
        } else {
          console.log('⚠️ colors.xml not found. Creating a new one...');
          
          // Create a new colors.xml file
          fs.mkdirSync(path.join(androidResDir, 'values'), { recursive: true });
          fs.writeFileSync(
            colorsPath,
            `<resources>
  <color name="splashscreen_background">#ffffff</color>
  <color name="iconBackground">#42A5B2</color>
  <color name="colorPrimary">#42A5B2</color>
  <color name="colorPrimaryDark">#ffffff</color>
</resources>
`
          );
          console.log('✓ Created colors.xml with theme color #42A5B2');
        }
        
        // Copy favicon for web (optional)
        console.log('\nCopying favicon for web...');
        const assetsDir = path.join(rootDir, 'assets');
        if (!fs.existsSync(assetsDir)) {
          fs.mkdirSync(assetsDir, { recursive: true });
        }
        fs.copyFileSync(path.resolve(faviconPath), path.join(assetsDir, 'favicon.png'));
        console.log('✓ Copied favicon.png to assets directory');
        
        console.log('\n=== Icon Setup Complete! ===');
        console.log('\nNext steps:');
        console.log('1. Run "npm run android" to rebuild your app with the new icons');
        console.log('2. If using iOS, you\'ll need to update the iOS icons separately');
        console.log('\nYour Gift Corner app now has the new icons! 🎁');
        
      } catch (error) {
        console.error('\nError during icon setup:', error.message);
        console.error('Please check the file paths and try again.');
      }
    });
  });
}); 