import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, TextInput as RNTextInput } from 'react-native';
import { Appbar, Text, Button, Card, TextInput, useTheme, HelperText } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define types for WiFi printer
interface WiFiPrinterDevice {
  ipAddress: string;
  port: number;
}

// Placeholder for WiFi printer functionality
const WiFiPrinter = {
  connectToPrinter: async (ipAddress: string, port: number) => {
    // Simulate connection
    return true;
  },
  disconnectPrinter: async () => {
    // Simulate disconnection
    return true;
  },
  printTestPage: async () => {
    // Simulate printing
    return true;
  }
};

export default function WiFiPrinterScreen() {
  const theme = useTheme();
  const router = useRouter();
  const [ipAddress, setIpAddress] = useState('');
  const [port, setPort] = useState('9100'); // Default port for many printers
  const [isConnecting, setIsConnecting] = useState(false);
  const [isPrinting, setIsPrinting] = useState(false);
  const [connectedPrinter, setConnectedPrinter] = useState<WiFiPrinterDevice | null>(null);
  const [ipAddressError, setIpAddressError] = useState('');

  useEffect(() => {
    // Load saved WiFi printer if any
    const loadSavedPrinter = async () => {
      try {
        const savedPrinter = await AsyncStorage.getItem('saved_wifi_printer');
        if (savedPrinter) {
          const printer = JSON.parse(savedPrinter) as WiFiPrinterDevice;
          setConnectedPrinter(printer);
          setIpAddress(printer.ipAddress);
          setPort(printer.port.toString());
        }
      } catch (error) {
        console.error('Failed to load saved WiFi printer:', error);
      }
    };

    loadSavedPrinter();
  }, []);

  const validateIpAddress = (ip: string): boolean => {
    const ipPattern = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipPattern.test(ip);
  };

  const handleConnect = async () => {
    // Validate IP address
    if (!validateIpAddress(ipAddress)) {
      setIpAddressError('Please enter a valid IP address (e.g., *************)');
      return;
    } else {
      setIpAddressError('');
    }

    // Validate port
    const portNumber = parseInt(port, 10);
    if (isNaN(portNumber) || portNumber < 1 || portNumber > 65535) {
      Alert.alert('Invalid Port', 'Please enter a valid port number (1-65535).');
      return;
    }

    setIsConnecting(true);
    try {
      const connected = await WiFiPrinter.connectToPrinter(ipAddress, portNumber);
      if (connected) {
        const printerInfo: WiFiPrinterDevice = { ipAddress, port: portNumber };
        setConnectedPrinter(printerInfo);
        await AsyncStorage.setItem('saved_wifi_printer', JSON.stringify(printerInfo));
        Alert.alert('Connected', `Successfully connected to printer at ${ipAddress}:${port}.`);
      } else {
        Alert.alert('Connection Failed', 'Could not connect to the printer with the provided information.');
      }
    } catch (error) {
      console.error(error);
      Alert.alert('Connection Error', 'An error occurred while connecting to the printer.');
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    try {
      await WiFiPrinter.disconnectPrinter();
      setConnectedPrinter(null);
      await AsyncStorage.removeItem('saved_wifi_printer');
      Alert.alert('Disconnected', 'Printer disconnected successfully.');
    } catch (error) {
      console.error(error);
      Alert.alert('Disconnection Error', 'An error occurred while disconnecting from the printer.');
    }
  };

  const handleTestPrint = async () => {
    if (!connectedPrinter) {
      Alert.alert('No Printer', 'Please connect to a printer first.');
      return;
    }

    setIsPrinting(true);
    try {
      await WiFiPrinter.printTestPage();
      Alert.alert('Test Print', 'Test page sent to the printer.');
    } catch (error) {
      console.error(error);
      Alert.alert('Print Error', 'Failed to send test print to the printer.');
    } finally {
      setIsPrinting(false);
    }
  };

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: 16,
    },
    card: {
      marginBottom: 16,
    },
    input: {
      marginBottom: 8,
      backgroundColor: theme.colors.surfaceVariant,
    },
    inputContainer: {
      marginBottom: 8,
    },
    connectedPrinter: {
      padding: 16,
      backgroundColor: theme.colors.primaryContainer,
      borderRadius: 8,
      marginBottom: 16,
    },
    connectedInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 8,
    },
    infoLabel: {
      fontWeight: 'bold',
      marginRight: 8,
      color: theme.colors.onPrimaryContainer,
    },
    infoValue: {
      color: theme.colors.onPrimaryContainer,
    },
    buttonContainer: {
      marginTop: 16,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    helpText: {
      marginTop: 16,
      color: theme.colors.onSurfaceVariant,
    }
  });

  return (
    <SafeAreaView style={styles.safeArea} edges={['top', 'bottom', 'left', 'right']}>
      <Appbar.Header statusBarHeight={0}>
        <Appbar.BackAction onPress={() => router.back()} />
        <Appbar.Content title="Wi-Fi Printer Setup" />
      </Appbar.Header>
      
      <View style={styles.content}>
        {connectedPrinter ? (
          <View>
            <Card style={styles.connectedPrinter}>
              <Card.Content>
                <Text variant="titleMedium">Connected Printer</Text>
                <View style={styles.connectedInfo}>
                  <Text style={styles.infoLabel}>IP Address:</Text>
                  <Text style={styles.infoValue}>{connectedPrinter.ipAddress}</Text>
                </View>
                <View style={styles.connectedInfo}>
                  <Text style={styles.infoLabel}>Port:</Text>
                  <Text style={styles.infoValue}>{connectedPrinter.port}</Text>
                </View>
              </Card.Content>
              <Card.Actions>
                <Button 
                  onPress={handleTestPrint}
                  loading={isPrinting}
                  disabled={isPrinting}
                  icon="printer"
                >
                  Test Print
                </Button>
                <Button 
                  onPress={handleDisconnect}
                  textColor={theme.colors.error}
                  icon="wifi-off"
                >
                  Disconnect
                </Button>
              </Card.Actions>
            </Card>
          </View>
        ) : (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={{ marginBottom: 16 }}>
                Connect to Wi-Fi Printer
              </Text>
              
              <View style={styles.inputContainer}>
                <TextInput
                  label="IP Address"
                  value={ipAddress}
                  onChangeText={(text) => {
                    setIpAddress(text);
                    setIpAddressError('');
                  }}
                  keyboardType="numeric"
                  placeholder="*************"
                  mode="outlined"
                  error={!!ipAddressError}
                  style={styles.input}
                />
                {ipAddressError ? <HelperText type="error">{ipAddressError}</HelperText> : null}
              </View>
              
              <View style={styles.inputContainer}>
                <TextInput
                  label="Port"
                  value={port}
                  onChangeText={setPort}
                  keyboardType="numeric"
                  placeholder="9100"
                  mode="outlined"
                  style={styles.input}
                />
                <HelperText type="info">
                  Default port for most printers is 9100
                </HelperText>
              </View>
              
              <Button
                mode="contained"
                onPress={handleConnect}
                loading={isConnecting}
                disabled={isConnecting}
                icon="wifi"
                style={{ marginTop: 8 }}
              >
                Connect to Printer
              </Button>
            </Card.Content>
          </Card>
        )}
        
        <Text style={styles.helpText}>
          Note: To use a Wi-Fi printer, your printer must be connected to the same network as your device.
          Check your printer's manual for information on how to find its IP address.
        </Text>
      </View>
    </SafeAreaView>
  );
} 