import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, Platform } from 'react-native';
import { Appbar, Text, Button, Card, List, useTheme, ActivityIndicator, Divider } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define types for USB printer device
interface USBPrinterDevice {
  id: string;
  name: string;
  serialNumber: string;
}

// This is a placeholder for USB printer functionality
// Real implementation would use a library for USB communication
const USBPrinter = {
  listDevices: async (): Promise<USBPrinterDevice[]> => {
    // Simulated devices
    return [
      { id: 'usb-001', name: 'Epson TM-T20III', serialNumber: 'EPSN12345' },
      { id: 'usb-002', name: 'Star Micronics TSP143', serialNumber: 'STAR67890' },
      { id: 'usb-003', name: 'Zebra ZD410', serialNumber: 'ZBRA54321' },
    ];
  },
  connectToPrinter: async (device: USBPrinterDevice): Promise<boolean> => {
    // Simulate connection
    return true;
  },
  disconnectPrinter: async (): Promise<boolean> => {
    // Simulate disconnection
    return true;
  },
  printTestPage: async (): Promise<boolean> => {
    // Simulate test print
    return true;
  }
};

export default function USBPrinterScreen() {
  const theme = useTheme();
  const router = useRouter();
  const [devices, setDevices] = useState<USBPrinterDevice[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [connectedDevice, setConnectedDevice] = useState<USBPrinterDevice | null>(null);
  const [isPrinting, setIsPrinting] = useState(false);

  useEffect(() => {
    // Load saved USB printer if any
    const loadSavedPrinter = async () => {
      try {
        const savedPrinter = await AsyncStorage.getItem('saved_usb_printer');
        if (savedPrinter) {
          setConnectedDevice(JSON.parse(savedPrinter));
        }
      } catch (error) {
        console.error('Failed to load saved USB printer:', error);
      }
    };

    loadSavedPrinter();
  }, []);

  const scanForDevices = async () => {
    if (Platform.OS === 'web') {
      Alert.alert('Not Supported', 'USB printer setup is not supported on web.');
      return;
    }

    setIsScanning(true);
    try {
      const foundDevices = await USBPrinter.listDevices();
      setDevices(foundDevices);
    } catch (error) {
      console.error(error);
      Alert.alert('Scan Error', 'Failed to scan for USB devices.');
    } finally {
      setIsScanning(false);
    }
  };

  const connectToDevice = async (device: USBPrinterDevice) => {
    try {
      const connected = await USBPrinter.connectToPrinter(device);
      if (connected) {
        setConnectedDevice(device);
        await AsyncStorage.setItem('saved_usb_printer', JSON.stringify(device));
        Alert.alert('Connected', `Successfully connected to ${device.name}.`);
      } else {
        Alert.alert('Connection Failed', 'Could not connect to the selected device.');
      }
    } catch (error) {
      console.error(error);
      Alert.alert('Connection Error', 'An error occurred while connecting to the printer.');
    }
  };

  const disconnectFromDevice = async () => {
    try {
      await USBPrinter.disconnectPrinter();
      setConnectedDevice(null);
      await AsyncStorage.removeItem('saved_usb_printer');
      Alert.alert('Disconnected', 'Printer disconnected successfully.');
    } catch (error) {
      console.error(error);
      Alert.alert('Disconnection Error', 'An error occurred while disconnecting from the printer.');
    }
  };

  const handleTestPrint = async () => {
    if (!connectedDevice) {
      Alert.alert('No Printer', 'Please connect to a printer first.');
      return;
    }

    setIsPrinting(true);
    try {
      await USBPrinter.printTestPage();
      Alert.alert('Test Print', 'Test page sent to the printer.');
    } catch (error) {
      console.error(error);
      Alert.alert('Print Error', 'Failed to send test print to the printer.');
    } finally {
      setIsPrinting(false);
    }
  };

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: 16,
    },
    card: {
      marginBottom: 16,
    },
    deviceList: {
      marginTop: 16,
    },
    deviceItem: {
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outlineVariant,
    },
    connectedDevice: {
      padding: 16,
      backgroundColor: theme.colors.primaryContainer,
      borderRadius: 8,
      marginBottom: 16,
    },
    infoRow: {
      flexDirection: 'row',
      marginTop: 8,
    },
    infoLabel: {
      fontWeight: 'bold',
      marginRight: 8,
      color: theme.colors.onPrimaryContainer,
    },
    infoValue: {
      color: theme.colors.onPrimaryContainer,
    },
    buttonContainer: {
      marginTop: 16,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    emptyText: {
      textAlign: 'center',
      padding: 16,
      color: theme.colors.onSurfaceVariant,
    },
    helpText: {
      marginTop: 16,
      color: theme.colors.onSurfaceVariant,
    }
  });

  return (
    <SafeAreaView style={styles.safeArea} edges={['top', 'bottom', 'left', 'right']}>
      <Appbar.Header statusBarHeight={0}>
        <Appbar.BackAction onPress={() => router.back()} />
        <Appbar.Content title="USB Printer Setup" />
      </Appbar.Header>
      
      <View style={styles.content}>
        {connectedDevice ? (
          <View>
            <Card style={styles.connectedDevice}>
              <Card.Content>
                <Text variant="titleMedium">Connected Printer</Text>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Name:</Text>
                  <Text style={styles.infoValue}>{connectedDevice.name}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Serial Number:</Text>
                  <Text style={styles.infoValue}>{connectedDevice.serialNumber}</Text>
                </View>
              </Card.Content>
              <Card.Actions>
                <Button 
                  onPress={handleTestPrint}
                  loading={isPrinting}
                  disabled={isPrinting}
                  icon="printer"
                >
                  Test Print
                </Button>
                <Button 
                  onPress={disconnectFromDevice}
                  textColor={theme.colors.error}
                  icon="link-off"
                >
                  Disconnect
                </Button>
              </Card.Actions>
            </Card>
          </View>
        ) : (
          <View>
            <Button 
              mode="contained" 
              onPress={scanForDevices} 
              loading={isScanning}
              disabled={isScanning}
              icon="usb"
              style={{ marginBottom: 16 }}
            >
              {isScanning ? 'Scanning...' : 'Scan for USB Printers'}
            </Button>

            {isScanning ? (
              <ActivityIndicator animating={true} size="large" />
            ) : devices.length > 0 ? (
              <Card>
                <Card.Content>
                  <Text variant="titleMedium" style={{ marginBottom: 8 }}>Available USB Printers</Text>
                  {devices.map((device, index) => (
                    <React.Fragment key={device.id}>
                      {index > 0 && <Divider />}
                      <List.Item
                        title={device.name}
                        description={`S/N: ${device.serialNumber}`}
                        onPress={() => connectToDevice(device)}
                        right={props => <List.Icon {...props} icon="usb-port" />}
                        style={styles.deviceItem}
                      />
                    </React.Fragment>
                  ))}
                </Card.Content>
              </Card>
            ) : (
              <Text style={styles.emptyText}>
                No USB printers found. Tap "Scan" to search for available USB printers.
              </Text>
            )}
          </View>
        )}
        
        <Text style={styles.helpText}>
          Note: Make sure your USB printer is connected to your device and turned on.
          Some devices may require additional permissions or drivers to access USB peripherals.
        </Text>
      </View>
    </SafeAreaView>
  );
} 