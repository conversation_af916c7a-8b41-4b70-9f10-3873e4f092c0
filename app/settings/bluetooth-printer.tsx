import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, Alert, Platform, PermissionsAndroid } from 'react-native';
import { Appbar, Text, Button, Card, ActivityIndicator, Divider, List, useTheme } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define types for Bluetooth device
interface BluetoothDevice {
  id: string;
  name: string;
  address: string;
}

// Placeholder for the Bluetooth printer module
// In a real implementation, you would use a library like react-native-bluetooth-escpos-printer
// This is a placeholder to demonstrate the UI flow
const BluetoothPrinter = {
  isBluetoothEnabled: async () => {
    // In a real app, check if Bluetooth is enabled
    return true;
  },
  scanDevices: async () => {
    // Simulate finding devices
    return [
      { id: '1', name: 'Thermal Printer 58mm', address: '00:11:22:33:44:55' },
      { id: '2', name: 'Epson TM-T20', address: '55:44:33:22:11:00' },
      { id: '3', name: 'Star Micronics TSP100', address: 'AA:BB:CC:DD:EE:FF' },
    ] as BluetoothDevice[];
  },
  connectPrinter: async (device: BluetoothDevice) => {
    // Simulate connecting to a printer
    return true;
  },
  disconnectPrinter: async () => {
    // Simulate disconnecting
    return true;
  },
  printTest: async () => {
    // Simulate test print
    return true;
  }
};

export default function BluetoothPrinterScreen() {
  const theme = useTheme();
  const router = useRouter();
  const [isScanning, setIsScanning] = useState(false);
  const [devices, setDevices] = useState<BluetoothDevice[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<BluetoothDevice | null>(null);
  const [connectedDevice, setConnectedDevice] = useState<BluetoothDevice | null>(null);
  const [isPrinting, setIsPrinting] = useState(false);

  useEffect(() => {
    const checkPermissionsAndBluetooth = async () => {
      if (Platform.OS === 'android') {
        try {
          const granted = await PermissionsAndroid.requestMultiple([
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
            PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN
          ]);
          
          if (
            granted['android.permission.ACCESS_FINE_LOCATION'] !== PermissionsAndroid.RESULTS.GRANTED ||
            (granted['android.permission.BLUETOOTH_CONNECT'] !== undefined && 
             granted['android.permission.BLUETOOTH_CONNECT'] !== PermissionsAndroid.RESULTS.GRANTED) ||
            (granted['android.permission.BLUETOOTH_SCAN'] !== undefined && 
             granted['android.permission.BLUETOOTH_SCAN'] !== PermissionsAndroid.RESULTS.GRANTED)
          ) {
            // Check if any permission was denied with "never ask again"
            if (
              granted['android.permission.ACCESS_FINE_LOCATION'] === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN ||
              (granted['android.permission.BLUETOOTH_CONNECT'] !== undefined && 
               granted['android.permission.BLUETOOTH_CONNECT'] === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) ||
              (granted['android.permission.BLUETOOTH_SCAN'] !== undefined && 
               granted['android.permission.BLUETOOTH_SCAN'] === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN)
            ) {
              Alert.alert(
                'Permission Required', 
                'Bluetooth and Location permissions are needed for this feature. Please go to App Settings and manually grant these permissions.',
                [
                  { text: "Cancel", style: "cancel" },
                  // Optionally, add a button to open app settings if possible/desired, though this is platform-specific
                  // { text: "Open Settings", onPress: () => Linking.openSettings() } 
                ]
              );
            } else {
              Alert.alert('Permission Required', 'Please grant Bluetooth and Location permissions to use this feature.');
            }
          }
        } catch (err) {
          console.warn(err);
        }
      }

      // Check if Bluetooth is enabled
      try {
        const enabled = await BluetoothPrinter.isBluetoothEnabled();
        if (!enabled) {
          Alert.alert('Bluetooth Disabled', 'Please enable Bluetooth to connect to a printer.');
        }
      } catch (error) {
        console.error(error);
      }
    };

    // Load saved printer if any
    const loadSavedPrinter = async () => {
      try {
        const savedPrinter = await AsyncStorage.getItem('saved_bluetooth_printer');
        if (savedPrinter) {
          setConnectedDevice(JSON.parse(savedPrinter));
        }
      } catch (error) {
        console.error('Failed to load saved printer:', error);
      }
    };

    checkPermissionsAndBluetooth();
    loadSavedPrinter();
  }, []);

  const scanForDevices = async () => {
    setIsScanning(true);
    try {
      const foundDevices = await BluetoothPrinter.scanDevices();
      setDevices(foundDevices);
    } catch (error) {
      console.error(error);
      Alert.alert('Scan Error', 'Failed to scan for Bluetooth devices.');
    } finally {
      setIsScanning(false);
    }
  };

  const connectToDevice = async (device: BluetoothDevice) => {
    try {
      const connected = await BluetoothPrinter.connectPrinter(device);
      if (connected) {
        setConnectedDevice(device);
        setSelectedDevice(null);
        await AsyncStorage.setItem('saved_bluetooth_printer', JSON.stringify(device));
        Alert.alert('Connected', `Successfully connected to ${device.name}.`);
      } else {
        Alert.alert('Connection Failed', 'Could not connect to the selected device.');
      }
    } catch (error) {
      console.error(error);
      Alert.alert('Connection Error', 'An error occurred while connecting to the printer.');
    }
  };

  const disconnectFromDevice = async () => {
    try {
      await BluetoothPrinter.disconnectPrinter();
      setConnectedDevice(null);
      await AsyncStorage.removeItem('saved_bluetooth_printer');
      Alert.alert('Disconnected', 'Printer disconnected successfully.');
    } catch (error) {
      console.error(error);
      Alert.alert('Disconnection Error', 'An error occurred while disconnecting from the printer.');
    }
  };

  const printTestPage = async () => {
    if (!connectedDevice) {
      Alert.alert('No Printer', 'Please connect to a printer first.');
      return;
    }

    setIsPrinting(true);
    try {
      await BluetoothPrinter.printTest();
      Alert.alert('Test Print', 'Test page sent to the printer.');
    } catch (error) {
      console.error(error);
      Alert.alert('Print Error', 'Failed to send test print to the printer.');
    } finally {
      setIsPrinting(false);
    }
  };

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: 16,
    },
    card: {
      marginBottom: 16,
    },
    deviceItem: {
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outlineVariant,
    },
    deviceName: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.onSurface,
    },
    deviceAddress: {
      fontSize: 14,
      color: theme.colors.onSurfaceVariant,
    },
    connectedDevice: {
      padding: 16,
      backgroundColor: theme.colors.primaryContainer,
      borderRadius: 8,
      marginBottom: 16,
    },
    buttonContainer: {
      marginTop: 16,
      marginBottom: 16,
    },
    emptyText: {
      textAlign: 'center',
      padding: 16,
      color: theme.colors.onSurfaceVariant,
    }
  });

  return (
    <SafeAreaView style={styles.safeArea} edges={['top', 'bottom', 'left', 'right']}>
      <Appbar.Header statusBarHeight={0}>
        <Appbar.BackAction onPress={() => router.back()} />
        <Appbar.Content title="Bluetooth Printer Setup" />
      </Appbar.Header>
      
      <View style={styles.content}>
        {connectedDevice ? (
          <View>
            <Card style={styles.connectedDevice}>
              <Card.Content>
                <Text variant="titleMedium">Connected Printer</Text>
                <Text variant="bodyLarge">{connectedDevice.name}</Text>
                <Text variant="bodyMedium">{connectedDevice.address}</Text>
              </Card.Content>
              <Card.Actions>
                <Button 
                  onPress={printTestPage} 
                  loading={isPrinting}
                  disabled={isPrinting}
                  icon="printer"
                >
                  Test Print
                </Button>
                <Button 
                  onPress={disconnectFromDevice} 
                  textColor={theme.colors.error}
                  icon="bluetooth-off"
                >
                  Disconnect
                </Button>
              </Card.Actions>
            </Card>
          </View>
        ) : (
          <View>
            <Button 
              mode="contained" 
              onPress={scanForDevices} 
              loading={isScanning}
              disabled={isScanning}
              icon="bluetooth-connect"
              style={{ marginBottom: 16 }}
            >
              {isScanning ? 'Scanning...' : 'Scan for Bluetooth Printers'}
            </Button>

            {isScanning ? (
              <ActivityIndicator animating={true} size="large" />
            ) : devices.length > 0 ? (
              <Card>
                <Card.Content>
                  <Text variant="titleMedium" style={{ marginBottom: 8 }}>Available Printers</Text>
                  <FlatList
                    data={devices}
                    keyExtractor={(item) => item.id}
                    renderItem={({ item }) => (
                      <TouchableOpacity 
                        style={styles.deviceItem}
                        onPress={() => connectToDevice(item)}
                      >
                        <Text style={styles.deviceName}>{item.name}</Text>
                        <Text style={styles.deviceAddress}>{item.address}</Text>
                      </TouchableOpacity>
                    )}
                    ItemSeparatorComponent={() => <Divider />}
                  />
                </Card.Content>
              </Card>
            ) : (
              <Text style={styles.emptyText}>
                No Bluetooth printers found. Tap "Scan" to search for available printers.
              </Text>
            )}
          </View>
        )}
      </View>
    </SafeAreaView>
  );
} 