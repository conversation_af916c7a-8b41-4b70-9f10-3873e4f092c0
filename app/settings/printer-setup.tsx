import React from 'react';
import { View, StyleSheet, Platform, TouchableOpacity } from 'react-native';
import { Appbar, Button, Card, Text, useTheme } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';

export default function PrinterSetupScreen() {
  const theme = useTheme();
  const router = useRouter();

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    container: {
      flex: 1,
    },
    header: {
      backgroundColor: theme.colors.elevation.level2,
    },
    content: {
      flex: 1,
      padding: 16,
    },
    card: {
      marginBottom: 16,
      backgroundColor: theme.colors.surface,
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      marginBottom: 8,
      color: theme.colors.onSurface,
    },
    buttonContainer: {
      marginTop: 8,
    },
    buttonStyle: {
      marginBottom:12,
    }
  });

  return (
    <SafeAreaView style={styles.safeArea} edges={['top', 'bottom', 'left', 'right']}>
      <Appbar.Header style={styles.header} statusBarHeight={0} elevated>
        <Appbar.BackAction onPress={() => router.back()} color={theme.colors.onSurface} />
        <Appbar.Content title="Printer Setup" titleStyle={{ color: theme.colors.onSurface, fontWeight: 'bold' }} />
      </Appbar.Header>
      <View style={styles.content}>
        <Text style={[styles.title, { marginBottom: 20, color: theme.colors.onSurfaceVariant }]}>
          Select your printer connection type:
        </Text>

        <Card style={styles.card}>
          <Card.Content>
            <Button 
              icon="bluetooth" 
              mode="contained" 
              onPress={() => router.push({
                pathname: '/PrinterSetupScreen',
                params: { initialType: 'bluetooth' }
              })}
              style={styles.buttonStyle}
              contentStyle={{paddingVertical: 8}}
              labelStyle={{fontSize: 16}}
            >
              Connect via Bluetooth
            </Button>
            <Button 
              icon="wifi" 
              mode="contained" 
              onPress={() => router.push({
                pathname: '/PrinterSetupScreen',
                params: { initialType: 'wifi' }
              })}
              style={styles.buttonStyle}
              buttonColor={theme.colors.secondaryContainer}
              textColor={theme.colors.onSecondaryContainer}
              contentStyle={{paddingVertical: 8}}
              labelStyle={{fontSize: 16}}
            >
              Connect via Wi-Fi
            </Button>
            <Button 
              icon="usb" 
              mode="contained" 
              onPress={() => router.push({
                pathname: '/PrinterSetupScreen',
                params: { initialType: 'usb' }
              })}
              style={styles.buttonStyle}
              buttonColor={theme.colors.tertiaryContainer}
              textColor={theme.colors.onTertiaryContainer}
              contentStyle={{paddingVertical: 8}}
              labelStyle={{fontSize: 16}}
            >
              Connect via USB
            </Button>
          </Card.Content>
        </Card>
      </View>
    </SafeAreaView>
  );
} 