import { DarkTheme, DefaultTheme, ThemeProvider as NavigationThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { PaperProvider } from 'react-native-paper';
import 'react-native-reanimated';

import { CustomDarkTheme, CustomLightTheme } from '@/constants/theme';
import { ThemeProvider, useThemeContext } from './ThemeContext';
import { DatabaseProvider } from '@/components/DatabaseProvider';

// Create an inner layout component that has access to the theme context
function InnerLayout() {
  const { activeTheme } = useThemeContext();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  const paperTheme = activeTheme === 'dark' ? CustomDarkTheme : CustomLightTheme;
  const navTheme = activeTheme === 'dark' ? DarkTheme : DefaultTheme;

  return (
    <PaperProvider theme={paperTheme}>
      <NavigationThemeProvider value={navTheme}>
        <DatabaseProvider>
        <Stack>
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen name="+not-found" />
        </Stack>
        <StatusBar style={activeTheme === 'dark' ? 'light' : 'dark'} />
        </DatabaseProvider>
      </NavigationThemeProvider>
    </PaperProvider>
  );
}

// Root layout that provides the theme context
export default function RootLayout() {
  return (
    <ThemeProvider>
      <InnerLayout />
    </ThemeProvider>
  );
}
