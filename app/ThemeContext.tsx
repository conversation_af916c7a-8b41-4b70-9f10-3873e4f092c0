import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useColorScheme as useDeviceColorScheme } from 'react-native';

// Define theme types
type ThemeType = 'light' | 'dark' | 'system';

// Context interface
interface ThemeContextType {
  theme: ThemeType;
  activeTheme: 'light' | 'dark'; // The actual theme being used (after processing 'system')
  isLoadingTheme: boolean;
  setTheme: (theme: ThemeType) => Promise<void>;
}

// Create context with default values
const ThemeContext = createContext<ThemeContextType>({
  theme: 'system',
  activeTheme: 'light',
  isLoadingTheme: true,
  setTheme: async () => {},
});

// Storage key
const THEME_STORAGE_KEY = 'gift_corner_theme';

// Provider component
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const deviceTheme = useDeviceColorScheme();
  const [theme, setThemeState] = useState<ThemeType>('system');
  const [isLoadingTheme, setIsLoadingTheme] = useState(true);

  // Compute the active theme based on theme setting and device theme
  const activeTheme = theme === 'system' 
    ? (deviceTheme || 'light') 
    : theme;

  // Load the saved theme on initial mount
  useEffect(() => {
    const loadSavedTheme = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (savedTheme) {
          setThemeState(savedTheme as ThemeType);
        }
      } catch (error) {
        console.error('Failed to load theme setting:', error);
      } finally {
        setIsLoadingTheme(false);
      }
    };

    loadSavedTheme();
  }, []);

  // Save and update theme
  const setTheme = async (newTheme: ThemeType) => {
    // Update state immediately for a responsive UI
    setThemeState(newTheme); 
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, newTheme);
    } catch (error) {
      console.error('Failed to save theme setting:', error);
      // Optionally, handle the error, e.g., revert the theme if saving fails
      // For now, we'll keep the optimistic update.
    }
  };

  return (
    <ThemeContext.Provider value={{ theme, activeTheme, isLoadingTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook for using the theme context
export const useThemeContext = () => useContext(ThemeContext);

// Adding default export to fix the warning
export default ThemeProvider; 