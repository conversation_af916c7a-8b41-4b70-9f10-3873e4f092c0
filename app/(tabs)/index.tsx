import { useColorScheme } from '@/hooks/useColorScheme';
import { Bill } from '@/types/bill';
import { getBills } from '@/utils/storage';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Link, useFocusEffect, useRouter, useNavigation } from 'expo-router';
import React, { useCallback, useState } from 'react';
import { Platform, SafeAreaView, ScrollView, StatusBar, StyleSheet, View, Dimensions, TouchableOpacity } from 'react-native';
import Constants from 'expo-constants';
import { Avatar, Button, Card, Chip, Divider, Surface, Text, useTheme, IconButton } from 'react-native-paper';
import Animated, { FadeInDown, FadeInRight, FadeInUp, FadeInLeft } from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';

const AnimatedCard = Animated.createAnimatedComponent(Card);
const AnimatedView = Animated.createAnimatedComponent(View);
const AnimatedSurface = Animated.createAnimatedComponent(Surface);
const screenWidth = Dimensions.get('window').width;

export default function HomeScreen() {
  const theme = useTheme();
  const colorScheme = useColorScheme();
  const router = useRouter();
  const navigation = useNavigation();
  const [recentBills, setRecentBills] = useState<Bill[]>([]);
  const [totalToday, setTotalToday] = useState(0);
  const [todayBillsCount, setTodayBillsCount] = useState(0);
  const [weeklyTotal, setWeeklyTotal] = useState(0);
  const [monthlyTotal, setMonthlyTotal] = useState(0);
  const [isPending, setIsPending] = useState(true);

  const loadData = useCallback(async () => {
    setIsPending(true);
    try {
      const bills = await getBills();

      // Calculate today's data
      const today = new Date().toISOString().split('T')[0];
      const todayBills = bills.filter(
        (bill) => new Date(bill.date).toISOString().split('T')[0] === today
      );
      const total = todayBills.reduce((sum, bill) => sum + (bill.finalAmount ?? bill.totalAmount), 0);
      setTotalToday(total);
      setRecentBills(bills.slice(-4).reverse());
      setTodayBillsCount(todayBills.length);

      // Calculate weekly total
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      const weeklyBills = bills.filter(
        (bill) => new Date(bill.date) >= oneWeekAgo
      );
      const weeklySum = weeklyBills.reduce((sum, bill) => sum + (bill.finalAmount ?? bill.totalAmount), 0);
      setWeeklyTotal(weeklySum);

      // Calculate monthly total
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
      const monthlyBills = bills.filter(
        (bill) => new Date(bill.date) >= oneMonthAgo
      );
      const monthlySum = monthlyBills.reduce((sum, bill) => sum + (bill.finalAmount ?? bill.totalAmount), 0);
      setMonthlyTotal(monthlySum);
    } catch (error) {
      console.error('Error loading data for HomeScreen:', error);
    } finally {
      setIsPending(false);
    }
  }, []);

  useFocusEffect(
    useCallback(() => {
      loadData();
      return () => {}; // Optional cleanup
    }, [loadData])
  );

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Helper function to get greeting based on time of day
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 18) return 'Good Afternoon';
    return 'Good Evening';
  };

  // Format date in a more readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', { day: 'numeric', month: 'short' });
    }
  };

  // Modern Dynamic Styles
  const dynamicStyles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    container: {
      flex: 1,
    },

    // Modern Header Styles
    modernHeader: {
      backgroundColor: theme.colors.surface,
      paddingTop: Constants.statusBarHeight + 16,
      paddingBottom: 16,
      paddingHorizontal: 20,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outlineVariant,
    },
    headerContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    menuButton: {
      padding: 8,
      borderRadius: 12,
      backgroundColor: theme.colors.surfaceVariant,
    },
    headerTitleContainer: {
      flex: 1,
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: theme.colors.onSurface,
      letterSpacing: 0.5,
    },
    headerSubtitle: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '500',
      marginTop: 2,
    },
    headerActions: {
      flexDirection: 'row',
      gap: 8,
    },
    profileButton: {
      padding: 8,
      borderRadius: 12,
      backgroundColor: theme.colors.surfaceVariant,
    },

    // Content Styles
    scrollViewContent: {
      paddingBottom: 100,
    },

    // Welcome Section
    welcomeSection: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 24,
    },
    welcomeContent: {
      flex: 1,
    },
    welcomeText: {
      fontSize: 24,
      fontWeight: '700',
      color: theme.colors.onSurface,
      marginBottom: 4,
    },
    welcomeSubtext: {
      fontSize: 14,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '500',
    },
    dateContainer: {
      backgroundColor: theme.colors.primaryContainer,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
    },
    dateText: {
      fontSize: 12,
      fontWeight: '600',
      color: theme.colors.onPrimaryContainer,
    },
    // Stats Grid Styles
    statsGrid: {
      paddingHorizontal: 20,
      marginBottom: 24,
    },
    mainStatsCard: {
      marginBottom: 16,
    },
    todayCard: {
      borderRadius: 20,
      backgroundColor: theme.colors.primary,
      overflow: 'hidden',
    },
    todayCardContent: {
      padding: 24,
    },
    todayHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    todayIconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 12,
    },
    todayLabel: {
      fontSize: 16,
      fontWeight: '600',
      color: '#FFFFFF',
      opacity: 0.9,
    },
    todayAmount: {
      fontSize: 36,
      fontWeight: '800',
      color: '#FFFFFF',
      marginBottom: 12,
      letterSpacing: -1,
    },
    todayMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    todayBillCount: {
      fontSize: 14,
      color: 'rgba(255, 255, 255, 0.8)',
      fontWeight: '500',
    },
    growthIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.15)',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    growthText: {
      fontSize: 12,
      fontWeight: '600',
      color: '#4CAF50',
      marginLeft: 2,
    },

    // Stats Row
    statsRow: {
      flexDirection: 'row',
      gap: 12,
    },
    statCard: {
      flex: 1,
    },
    statCardSurface: {
      borderRadius: 16,
      backgroundColor: theme.colors.surface,
    },
    statCardContent: {
      padding: 20,
      alignItems: 'center',
    },
    statIconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 12,
    },
    statValue: {
      fontSize: 20,
      fontWeight: '700',
      color: theme.colors.onSurface,
      marginBottom: 4,
      letterSpacing: -0.5,
    },
    statLabel: {
      fontSize: 12,
      fontWeight: '600',
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
    },
    // Quick Actions Section
    quickActionsSection: {
      paddingHorizontal: 20,
      marginBottom: 32,
    },
    quickActionsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    quickActionCard: {
      width: '48%',
      borderRadius: 16,
      overflow: 'hidden',
      elevation: 2,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    quickActionContent: {
      padding: 20,
      alignItems: 'center',
    },
    quickActionTitle: {
      fontSize: 16,
      fontWeight: '700',
      color: '#FFFFFF',
      marginTop: 8,
      marginBottom: 2,
    },
    quickActionSubtitle: {
      fontSize: 12,
      color: 'rgba(255, 255, 255, 0.8)',
      fontWeight: '500',
    },
    // Section Styles
    sectionTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: theme.colors.onSurface,
      marginBottom: 16,
      letterSpacing: 0.3,
    },

    // Recent Activity Section
    recentSection: {
      paddingHorizontal: 20,
      marginBottom: 32,
    },
    recentHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    viewAllButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 12,
      backgroundColor: theme.colors.primaryContainer,
    },
    viewAllText: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.primary,
      marginRight: 4,
    },
    recentBillsList: {
      gap: 12,
    },
    // Modern Bill Cards
    modernBillCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 16,
      padding: 16,
      elevation: 2,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.08,
      shadowRadius: 4,
      borderWidth: 1,
      borderColor: theme.colors.outlineVariant,
    },
    billCardContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    billCardLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    billIconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.primaryContainer,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 12,
    },
    billInfo: {
      flex: 1,
    },
    modernBillName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 2,
    },
    modernBillDate: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '500',
    },
    billCardRight: {
      alignItems: 'flex-end',
    },
    modernBillAmount: {
      fontSize: 16,
      fontWeight: '700',
      color: theme.colors.onSurface,
      marginBottom: 4,
    },
    modernStatusBadge: {
      paddingHorizontal: 8,
      paddingVertical: 2,
      borderRadius: 8,
    },
    modernStatusText: {
      fontSize: 10,
      fontWeight: '600',
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    billHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    billAvatar: {
      backgroundColor: theme.colors.primary,
      marginRight: 12,
    },
    billName: {
      fontSize: 17,
      fontWeight: '600',
      color: theme.colors.onSurface,
      letterSpacing: 0.2,
    },
    billAmount: {
      fontSize: 20,
      fontWeight: '700',
      color: theme.colors.primary,
      textAlign: 'right',
      letterSpacing: -0.3,
    },
    billDetailsRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: 8,
    },
    billDate: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      flexDirection: 'row',
      alignItems: 'center',
    },
    dateIcon: {
      marginRight: 4,
    },
    itemsList: {
      marginTop: 12,
      marginLeft: 52,
      padding: 12,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.outlineVariant,
    },
    emptyStateContainer: {
      padding: 32,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 20,
      marginTop: 20,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 0.12,
      shadowRadius: 6,
      elevation: 3,
      borderWidth: 1,
      borderColor: theme.colors.outlineVariant,
    },
    emptyStateIcon: {
      marginBottom: 20,
    },
    // Modern Empty State
    modernEmptyState: {
      alignItems: 'center',
      paddingVertical: 40,
      paddingHorizontal: 20,
    },
    emptyStateIconContainer: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: theme.colors.surfaceVariant,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 24,
    },
    modernEmptyTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: theme.colors.onSurface,
      marginBottom: 8,
      textAlign: 'center',
    },
    modernEmptySubtitle: {
      fontSize: 14,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      lineHeight: 20,
      marginBottom: 24,
    },
    modernEmptyButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 24,
      gap: 8,
    },
    modernEmptyButtonText: {
      fontSize: 14,
      fontWeight: '600',
      color: '#FFFFFF',
    },

    // Modern FAB
    modernFab: {
      position: 'absolute',
      bottom: 24,
      right: 24,
      zIndex: 100,
    },
    fabButton: {
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: theme.colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
      elevation: 6,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 0.2,
      shadowRadius: 6,
    },
  });

  const openDrawer = () => {
    (navigation as any).openDrawer();
  };

  return (
    <SafeAreaView style={dynamicStyles.safeArea}>
      <StatusBar barStyle={colorScheme === 'dark' ? "light-content" : "dark-content"} backgroundColor={theme.colors.surface} />

      {/* Modern Header */}
      <View style={dynamicStyles.modernHeader}>
        <View style={dynamicStyles.headerContent}>
          <TouchableOpacity onPress={openDrawer} style={dynamicStyles.menuButton}>
            <MaterialCommunityIcons name="menu" size={24} color={theme.colors.onSurface} />
          </TouchableOpacity>

          <View style={dynamicStyles.headerTitleContainer}>
            <Text style={dynamicStyles.headerTitle}>GIFT CORNER</Text>
            <Text style={dynamicStyles.headerSubtitle}>Dashboard</Text>
          </View>

          <View style={dynamicStyles.headerActions}>
            <Link href="/(tabs)/settings" asChild>
              <TouchableOpacity style={dynamicStyles.profileButton}>
                <MaterialCommunityIcons name="account-circle-outline" size={22} color={theme.colors.onSurface} />
              </TouchableOpacity>
            </Link>
          </View>
        </View>
      </View>

      {/* Content container */}
      <ScrollView
        style={dynamicStyles.container}
        contentContainerStyle={dynamicStyles.scrollViewContent}
        showsVerticalScrollIndicator={false}
      >

        {/* Welcome Section */}
        <AnimatedView entering={FadeInDown.delay(100).duration(600)} style={dynamicStyles.welcomeSection}>
          <View style={dynamicStyles.welcomeContent}>
            <Text style={dynamicStyles.welcomeText}>{getGreeting()}</Text>
            <Text style={dynamicStyles.welcomeSubtext}>Here's your business overview</Text>
          </View>
          <View style={dynamicStyles.dateContainer}>
            <Text style={dynamicStyles.dateText}>{new Date().toLocaleDateString('en-US', {
              weekday: 'short',
              month: 'short',
              day: 'numeric'
            })}</Text>
          </View>
        </AnimatedView>

        {/* Stats Grid */}
        <View style={dynamicStyles.statsGrid}>
          {/* Today's Sales - Main Card */}
          <AnimatedView entering={FadeInUp.delay(200).duration(600)} style={dynamicStyles.mainStatsCard}>
            <Surface style={dynamicStyles.todayCard} elevation={2}>
              <View style={dynamicStyles.todayCardContent}>
                <View style={dynamicStyles.todayHeader}>
                  <View style={dynamicStyles.todayIconContainer}>
                    <MaterialCommunityIcons
                      name="trending-up"
                      size={24}
                      color="#FFFFFF"
                    />
                  </View>
                  <Text style={dynamicStyles.todayLabel}>Today's Revenue</Text>
                </View>
                <Text style={dynamicStyles.todayAmount}>{formatCurrency(totalToday)}</Text>
                <View style={dynamicStyles.todayMeta}>
                  <Text style={dynamicStyles.todayBillCount}>{todayBillsCount} transactions</Text>
                  <View style={dynamicStyles.growthIndicator}>
                    <MaterialCommunityIcons name="arrow-up" size={14} color="#4CAF50" />
                    <Text style={dynamicStyles.growthText}>+12%</Text>
                  </View>
                </View>
              </View>
            </Surface>
          </AnimatedView>

          {/* Stats Row */}
          <View style={dynamicStyles.statsRow}>
            <AnimatedView entering={FadeInLeft.delay(300).duration(600)} style={dynamicStyles.statCard}>
              <Surface style={dynamicStyles.statCardSurface} elevation={1}>
                <View style={dynamicStyles.statCardContent}>
                  <View style={[dynamicStyles.statIconContainer, { backgroundColor: '#E3F2FD' }]}>
                    <MaterialCommunityIcons name="calendar-week" size={20} color="#1976D2" />
                  </View>
                  <Text style={dynamicStyles.statValue}>{formatCurrency(weeklyTotal)}</Text>
                  <Text style={dynamicStyles.statLabel}>This Week</Text>
                </View>
              </Surface>
            </AnimatedView>

            <AnimatedView entering={FadeInRight.delay(350).duration(600)} style={dynamicStyles.statCard}>
              <Surface style={dynamicStyles.statCardSurface} elevation={1}>
                <View style={dynamicStyles.statCardContent}>
                  <View style={[dynamicStyles.statIconContainer, { backgroundColor: '#FFF3E0' }]}>
                    <MaterialCommunityIcons name="calendar-month" size={20} color="#F57C00" />
                  </View>
                  <Text style={dynamicStyles.statValue}>{formatCurrency(monthlyTotal)}</Text>
                  <Text style={dynamicStyles.statLabel}>This Month</Text>
                </View>
              </Surface>
            </AnimatedView>
          </View>
        </View>

        {/* Quick Actions */}
        <AnimatedView entering={FadeInUp.delay(400).duration(600)} style={dynamicStyles.quickActionsSection}>
          <Text style={dynamicStyles.sectionTitle}>Quick Actions</Text>
          <View style={dynamicStyles.quickActionsGrid}>
            <TouchableOpacity
              style={[dynamicStyles.quickActionCard, { backgroundColor: theme.colors.primary }]}
              onPress={() => router.push('/(tabs)/new-bill')}
            >
              <View style={dynamicStyles.quickActionContent}>
                <MaterialCommunityIcons name="plus-circle" size={28} color="#FFFFFF" />
                <Text style={dynamicStyles.quickActionTitle}>New Bill</Text>
                <Text style={dynamicStyles.quickActionSubtitle}>Create invoice</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={[dynamicStyles.quickActionCard, { backgroundColor: '#4CAF50' }]}
              onPress={() => router.push('/(tabs)/history')}
            >
              <View style={dynamicStyles.quickActionContent}>
                <MaterialCommunityIcons name="history" size={28} color="#FFFFFF" />
                <Text style={dynamicStyles.quickActionTitle}>History</Text>
                <Text style={dynamicStyles.quickActionSubtitle}>View bills</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={[dynamicStyles.quickActionCard, { backgroundColor: '#FF9800' }]}
              onPress={() => router.push('/(tabs)/dashboard')}
            >
              <View style={dynamicStyles.quickActionContent}>
                <MaterialCommunityIcons name="chart-line" size={28} color="#FFFFFF" />
                <Text style={dynamicStyles.quickActionTitle}>Analytics</Text>
                <Text style={dynamicStyles.quickActionSubtitle}>View reports</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={[dynamicStyles.quickActionCard, { backgroundColor: '#9C27B0' }]}
              onPress={() => router.push('/(tabs)/settings')}
            >
              <View style={dynamicStyles.quickActionContent}>
                <MaterialCommunityIcons name="cog" size={28} color="#FFFFFF" />
                <Text style={dynamicStyles.quickActionTitle}>Settings</Text>
                <Text style={dynamicStyles.quickActionSubtitle}>Configure app</Text>
              </View>
            </TouchableOpacity>
          </View>
        </AnimatedView>

        {/* Recent Activity */}
        <AnimatedView entering={FadeInUp.delay(500).duration(600)} style={dynamicStyles.recentSection}>
          <View style={dynamicStyles.recentHeader}>
            <Text style={dynamicStyles.sectionTitle}>Recent Activity</Text>
            {recentBills.length > 0 && (
              <Link href="/(tabs)/history" asChild>
                <TouchableOpacity style={dynamicStyles.viewAllButton}>
                  <Text style={dynamicStyles.viewAllText}>View All</Text>
                  <MaterialCommunityIcons name="arrow-right" size={16} color={theme.colors.primary} />
                </TouchableOpacity>
              </Link>
            )}
          </View>

          <View style={dynamicStyles.recentBillsList}>
            {recentBills.length > 0 ? (
              recentBills.slice(0, 3).map((bill, index) => (
                <AnimatedView
                  key={bill.id}
                  entering={FadeInRight.delay(550 + (index * 100)).duration(600)}
                >
                  <TouchableOpacity
                    style={dynamicStyles.modernBillCard}
                    onPress={() => router.push(`/(tabs)/history?billId=${bill.id}`)}
                  >
                    <View style={dynamicStyles.billCardContent}>
                      <View style={dynamicStyles.billCardLeft}>
                        <View style={dynamicStyles.billIconContainer}>
                          <MaterialCommunityIcons
                            name="receipt"
                            size={20}
                            color={theme.colors.primary}
                          />
                        </View>
                        <View style={dynamicStyles.billInfo}>
                          <Text style={dynamicStyles.modernBillName}>
                            {bill.name || 'Walk-in Customer'}
                          </Text>
                          <Text style={dynamicStyles.modernBillDate}>
                            {formatDate(bill.date)} • {bill.items.length} items
                          </Text>
                        </View>
                      </View>

                      <View style={dynamicStyles.billCardRight}>
                        <Text style={dynamicStyles.modernBillAmount}>
                          {formatCurrency(bill.finalAmount ?? bill.totalAmount)}
                        </Text>
                        {bill.paymentStatus && (
                          <View style={[
                            dynamicStyles.modernStatusBadge,
                            {
                              backgroundColor: bill.paymentStatus === 'PAID'
                                ? '#E8F5E8'
                                : bill.paymentStatus === 'PARTIAL'
                                  ? '#FFF4E6'
                                  : '#FFEBEE'
                            }
                          ]}>
                            <Text style={[
                              dynamicStyles.modernStatusText,
                              {
                                color: bill.paymentStatus === 'PAID'
                                  ? '#2E7D32'
                                  : bill.paymentStatus === 'PARTIAL'
                                    ? '#F57C00'
                                    : '#D32F2F'
                              }
                            ]}>
                              {bill.paymentStatus}
                            </Text>
                          </View>
                        )}
                      </View>
                    </View>
                  </TouchableOpacity>
                </AnimatedView>
              ))
            ) : (
              <AnimatedView
                entering={FadeInUp.delay(600).duration(600)}
                style={dynamicStyles.modernEmptyState}
              >
                <View style={dynamicStyles.emptyStateIconContainer}>
                  <MaterialCommunityIcons
                    name="receipt"
                    size={64}
                    color={theme.colors.onSurfaceVariant}
                  />
                </View>
                <Text style={dynamicStyles.modernEmptyTitle}>No recent activity</Text>
                <Text style={dynamicStyles.modernEmptySubtitle}>
                  Start creating bills to see your recent transactions here
                </Text>
                <TouchableOpacity
                  style={dynamicStyles.modernEmptyButton}
                  onPress={() => router.push('/(tabs)/new-bill')}
                >
                  <MaterialCommunityIcons name="plus" size={20} color="#FFFFFF" />
                  <Text style={dynamicStyles.modernEmptyButtonText}>Create First Bill</Text>
                </TouchableOpacity>
              </AnimatedView>
            )}
          </View>
        </AnimatedView>
      </ScrollView>

      {/* Modern FAB */}
      <AnimatedView
        entering={FadeInUp.delay(700).duration(600)}
        style={dynamicStyles.modernFab}
      >
        <TouchableOpacity
          style={dynamicStyles.fabButton}
          onPress={() => router.push('/(tabs)/new-bill')}
        >
          <MaterialCommunityIcons name="plus" size={28} color="#FFFFFF" />
        </TouchableOpacity>
      </AnimatedView>
    </SafeAreaView>
  );
}
