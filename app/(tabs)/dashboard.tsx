import { useColorScheme } from '@/hooks/useColorScheme';
import { getBills } from '@/utils/storage';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useFocusEffect, useNavigation } from 'expo-router';
import { StatusBar as ExpoStatusBar } from 'expo-status-bar';
import React, { useCallback, useState } from 'react';
import { SafeAreaView, ScrollView, StyleSheet, View, Dimensions, Platform, StatusBar, TouchableOpacity } from 'react-native';
import Constants from 'expo-constants';
import { ActivityIndicator, Appbar, Button, Text, useTheme, Surface, IconButton } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';

// Import our components
import DateFilterSelector, { DateFilterOption } from '@/components/dashboard/DateFilterSelector';
import { getFilteredDateRange } from '@/components/dashboard/DateRangeUtils';
import { processAnalyticsData, AnalyticsData, Bill as AnalyticsBill } from '@/components/dashboard/AnalyticsProcessor';
import SummaryCards from '@/components/dashboard/SummaryCards';
import SalesChart from '@/components/dashboard/SalesChart';
import PopularItems from '@/components/dashboard/PopularItems';
import QuickTips from '@/components/dashboard/QuickTips';
import { Bill } from '@/types/bill';

const AnimatedView = Animated.createAnimatedComponent(View);
const screenWidth = Dimensions.get('window').width;

export default function DashboardScreen() {
  const theme = useTheme();
  const colorScheme = useColorScheme();
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(true);
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Date filter state
  const [currentFilter, setCurrentFilter] = useState<DateFilterOption>(DateFilterOption.ALL_TIME);
  const [customDateRange, setCustomDateRange] = useState<{
    startDate: Date | undefined;
    endDate: Date | undefined;
  }>({
    startDate: undefined,
    endDate: undefined,
  });

  // Handle filter changes
  const handleFilterChange = (filter: DateFilterOption, range?: { startDate: Date | undefined, endDate: Date | undefined }) => {
    setCurrentFilter(filter);
    if (range) {
      setCustomDateRange(range);
    }
    loadDashboardData(filter, range);
  };

  const loadDashboardData = useCallback(async (
    filterOption: DateFilterOption = currentFilter,
    dateRangeParam = customDateRange
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const allBills = await getBills();

      if (!allBills || allBills.length === 0) {
        setAnalytics(null);
        setIsLoading(false);
        return;
      }

      // Get date range for filtering
      const dateRange = getFilteredDateRange(filterOption, dateRangeParam);

      // Filter bills based on the selected date range
      const bills = allBills.filter(bill => {
        const billDate = new Date(bill.date);
        if (dateRange.start && billDate < dateRange.start) {
          return false;
        }
        if (dateRange.end && billDate > dateRange.end) {
          return false;
        }
        return true;
      });

      if (bills.length === 0) {
        setAnalytics(null);
        setIsLoading(false);
        return;
      }

      // Process the analytics data using our utility
      const analyticsData = processAnalyticsData(
        bills.map(bill => ({
          ...bill,
          items: bill.items.map(item => ({
            ...item,
            name: item.name || 'Unnamed Item'
          }))
        })) as unknown as AnalyticsBill[],
        dateRange,
        (opacity = 1) => {
          // Get a valid RGBA color string with proper opacity
          const hexOpacity = Math.round(opacity * 255).toString(16).padStart(2, '0');
          return theme.colors.primary + hexOpacity;
        },
        `${filterOption === DateFilterOption.ALL_TIME ? 'All Time' : filterOption}`
      );

      setAnalytics(analyticsData);
    } catch (e) {
      console.error("Failed to load dashboard data:", e);
      setError("Failed to load dashboard data. Please try again.");
      setAnalytics(null);
    } finally {
      setIsLoading(false);
    }
  }, [theme.colors.primary, currentFilter, customDateRange]);

  // Load data on screen focus
  useFocusEffect(
    useCallback(() => {
      loadDashboardData();
    }, [loadDashboardData])
  );

  const openDrawer = () => {
    (navigation as any).openDrawer();
  };

  // Modern Dashboard Styles
  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    container: {
      flex: 1,
    },

    // Modern Header Styles
    modernHeader: {
      backgroundColor: theme.colors.surface,
      paddingTop: Constants.statusBarHeight + 16,
      paddingBottom: 16,
      paddingHorizontal: 20,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outlineVariant,
    },
    headerContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    menuButton: {
      padding: 8,
      borderRadius: 12,
      backgroundColor: theme.colors.surfaceVariant,
    },
    headerTitleContainer: {
      flex: 1,
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: theme.colors.onSurface,
      letterSpacing: 0.5,
    },
    headerSubtitle: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '500',
      marginTop: 2,
    },
    headerActions: {
      flexDirection: 'row',
      gap: 8,
    },
    refreshButton: {
      padding: 8,
      borderRadius: 12,
      backgroundColor: theme.colors.primaryContainer,
    },

    // Content Styles
    scrollViewContent: {
      paddingBottom: 100,
    },

    // Filter Section
    filterSection: {
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: theme.colors.background,
    },
    filterCard: {
      borderRadius: 16,
      elevation: 2,
      backgroundColor: theme.colors.surface,
    },

    // Overview Cards Section
    overviewSection: {
      paddingHorizontal: 20,
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: theme.colors.onSurface,
      marginBottom: 16,
      letterSpacing: 0.3,
    },
    overviewGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    overviewCard: {
      width: '48%',
      backgroundColor: theme.colors.surface,
      borderRadius: 16,
      padding: 20,
      elevation: 2,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.08,
      shadowRadius: 4,
    },
    overviewCardHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    overviewIconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 12,
    },
    overviewCardTitle: {
      fontSize: 12,
      fontWeight: '600',
      color: theme.colors.onSurfaceVariant,
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    overviewCardValue: {
      fontSize: 24,
      fontWeight: '800',
      color: theme.colors.onSurface,
      marginBottom: 4,
      letterSpacing: -0.5,
    },
    overviewCardSubtitle: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '500',
    },
    filterContainer: {
      marginHorizontal: 16,
      marginTop: -60,
      marginBottom: 12,
      zIndex: 10,
    },
    scrollViewContent: {
      paddingHorizontal: 16,
      paddingBottom: 24,
    },
    sectionContainer: {
      marginBottom: 16,
      borderRadius: 16,
      overflow: 'hidden',
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginVertical: 16,
    },
    cardContainer: {
      borderRadius: 16,
      overflow: 'hidden',
      elevation: 2,
      marginBottom: 16,
      backgroundColor: theme.colors.surface,
    },
    cardHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outlineVariant,
    },
    cardTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginLeft: 8,
    },
    cardContent: {
      padding: 16,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 24,
    },
    emptyContainer: {
      padding: 24,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 16,
      marginTop: 16,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    emptyIcon: {
      marginBottom: 16,
    },
    emptyText: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginBottom: 8,
    },
    emptySubtext: {
      fontSize: 14,
      color: theme.colors.onSurfaceDisabled,
      textAlign: 'center',
      marginBottom: 16,
    },
    errorContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: 40,
    },
    errorText: {
      color: theme.colors.error,
      textAlign: 'center',
      marginTop: 8,
      marginBottom: 16,
    },

    // Chart Section Styles
    chartSection: {
      paddingHorizontal: 20,
      marginBottom: 24,
    },
    chartCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 20,
      padding: 24,
      elevation: 2,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.08,
      shadowRadius: 4,
    },
    chartHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 20,
    },
    chartTitle: {
      fontSize: 18,
      fontWeight: '700',
      color: theme.colors.onSurface,
      letterSpacing: 0.3,
    },
    chartPeriod: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '500',
      backgroundColor: theme.colors.surfaceVariant,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 8,
    },

    // Analytics Grid Styles
    analyticsSection: {
      paddingHorizontal: 20,
      marginBottom: 24,
    },
    analyticsGrid: {
      flexDirection: 'row',
      gap: 12,
    },
    analyticsCard: {
      flex: 1,
      backgroundColor: theme.colors.surface,
      borderRadius: 16,
      padding: 20,
      elevation: 2,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.08,
      shadowRadius: 4,
    },
    analyticsCardHeader: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    analyticsIconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 12,
    },
    analyticsCardContent: {
      flex: 1,
    },
    analyticsCardTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.onSurfaceVariant,
      marginBottom: 4,
    },
    analyticsCardValue: {
      fontSize: 20,
      fontWeight: '700',
      color: theme.colors.onSurface,
      letterSpacing: -0.3,
    },

    // Popular Items Styles
    popularItemsSection: {
      paddingHorizontal: 20,
      marginBottom: 24,
    },
    popularItemsCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 20,
      padding: 24,
      elevation: 2,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.08,
      shadowRadius: 4,
    },
    popularItemRow: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outlineVariant,
    },
    popularItemRank: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: theme.colors.primaryContainer,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 16,
    },
    popularItemRankText: {
      fontSize: 14,
      fontWeight: '700',
      color: theme.colors.primary,
    },
    popularItemInfo: {
      flex: 1,
    },
    popularItemName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 2,
    },
    popularItemCount: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '500',
    },

    // Loading and Empty States
    loadingText: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
      marginTop: 16,
      textAlign: 'center',
    },
    emptyStateContainer: {
      alignItems: 'center',
      paddingVertical: 40,
      paddingHorizontal: 20,
    },
    emptyStateIconContainer: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: theme.colors.surfaceVariant,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 24,
    },
    emptyStateTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: theme.colors.onSurface,
      marginBottom: 8,
      textAlign: 'center',
    },
    emptyStateSubtitle: {
      fontSize: 14,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      lineHeight: 20,
      marginBottom: 24,
    },
    emptyStateButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 24,
      gap: 8,
    },
    emptyStateButtonText: {
      fontSize: 14,
      fontWeight: '600',
      color: '#FFFFFF',
    },
  });

  // Loading state
  if (isLoading) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <ExpoStatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />

        {/* Modern Header */}
        <View style={styles.modernHeader}>
          <View style={styles.headerContent}>
            <TouchableOpacity onPress={openDrawer} style={styles.menuButton}>
              <MaterialCommunityIcons name="menu" size={24} color={theme.colors.onSurface} />
            </TouchableOpacity>

            <View style={styles.headerTitleContainer}>
              <Text style={styles.headerTitle}>Analytics</Text>
              <Text style={styles.headerSubtitle}>Business Insights</Text>
            </View>

            <View style={styles.headerActions}>
              <TouchableOpacity style={styles.refreshButton} disabled>
                <MaterialCommunityIcons name="refresh" size={22} color={theme.colors.primary} />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <View style={styles.loadingContainer}>
          <ActivityIndicator animating={true} size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading Analytics...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Error state
  if (error) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <ExpoStatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />

        {/* Modern Header */}
        <View style={styles.modernHeader}>
          <View style={styles.headerContent}>
            <TouchableOpacity onPress={openDrawer} style={styles.menuButton}>
              <MaterialCommunityIcons name="menu" size={24} color={theme.colors.onSurface} />
            </TouchableOpacity>

            <View style={styles.headerTitleContainer}>
              <Text style={styles.headerTitle}>Analytics</Text>
              <Text style={styles.headerSubtitle}>Business Insights</Text>
            </View>

            <View style={styles.headerActions}>
              <TouchableOpacity style={styles.refreshButton} onPress={() => loadDashboardData()}>
                <MaterialCommunityIcons name="refresh" size={22} color={theme.colors.primary} />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <View style={styles.emptyStateContainer}>
          <View style={styles.emptyStateIconContainer}>
            <MaterialCommunityIcons name="alert-circle-outline" size={64} color={theme.colors.error} />
          </View>
          <Text style={styles.emptyStateTitle}>Something went wrong</Text>
          <Text style={styles.emptyStateSubtitle}>{error}</Text>
          <TouchableOpacity style={styles.emptyStateButton} onPress={() => loadDashboardData()}>
            <MaterialCommunityIcons name="refresh" size={20} color="#FFFFFF" />
            <Text style={styles.emptyStateButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Empty data state
  if (!analytics) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <ExpoStatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />

        {/* Modern Header */}
        <View style={styles.modernHeader}>
          <View style={styles.headerContent}>
            <TouchableOpacity onPress={openDrawer} style={styles.menuButton}>
              <MaterialCommunityIcons name="menu" size={24} color={theme.colors.onSurface} />
            </TouchableOpacity>

            <View style={styles.headerTitleContainer}>
              <Text style={styles.headerTitle}>Analytics</Text>
              <Text style={styles.headerSubtitle}>Business Insights</Text>
            </View>

            <View style={styles.headerActions}>
              <TouchableOpacity style={styles.refreshButton} onPress={() => loadDashboardData()}>
                <MaterialCommunityIcons name="refresh" size={22} color={theme.colors.primary} />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Filter Section */}
        <AnimatedView entering={FadeInUp.delay(200).duration(600)} style={styles.filterSection}>
          <Surface style={styles.filterCard}>
            <DateFilterSelector
              currentFilter={currentFilter}
              customDateRange={customDateRange}
              onFilterChange={handleFilterChange}
            />
          </Surface>
        </AnimatedView>

        <View style={styles.emptyStateContainer}>
          <View style={styles.emptyStateIconContainer}>
            <MaterialCommunityIcons name="chart-areaspline" size={64} color={theme.colors.onSurfaceVariant} />
          </View>
          <Text style={styles.emptyStateTitle}>No analytics data</Text>
          <Text style={styles.emptyStateSubtitle}>
            Create some bills to see your business insights and analytics here
          </Text>
          <TouchableOpacity style={styles.emptyStateButton} onPress={() => router.push('/(tabs)/new-bill')}>
            <MaterialCommunityIcons name="plus" size={20} color="#FFFFFF" />
            <Text style={styles.emptyStateButtonText}>Create First Bill</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Main dashboard with data
  return (
    <SafeAreaView style={styles.safeArea}>
      <ExpoStatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />

      {/* Modern Header */}
      <View style={styles.modernHeader}>
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={openDrawer} style={styles.menuButton}>
            <MaterialCommunityIcons name="menu" size={24} color={theme.colors.onSurface} />
          </TouchableOpacity>

          <View style={styles.headerTitleContainer}>
            <Text style={styles.headerTitle}>Analytics</Text>
            <Text style={styles.headerSubtitle}>Business Insights</Text>
          </View>

          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.refreshButton} onPress={() => loadDashboardData()}>
              <MaterialCommunityIcons name="refresh" size={22} color={theme.colors.primary} />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Filter Section */}
        <AnimatedView entering={FadeInUp.delay(100).duration(600)} style={styles.filterSection}>
          <Surface style={styles.filterCard}>
            <DateFilterSelector
              currentFilter={currentFilter}
              customDateRange={customDateRange}
              onFilterChange={handleFilterChange}
            />
          </Surface>
        </AnimatedView>

        {/* Overview Cards */}
        <AnimatedView entering={FadeInUp.delay(200).duration(600)} style={styles.overviewSection}>
          <Text style={styles.sectionTitle}>Overview</Text>
          <View style={styles.overviewGrid}>
            <View style={styles.overviewCard}>
              <View style={styles.overviewCardHeader}>
                <View style={[styles.overviewIconContainer, { backgroundColor: '#E3F2FD' }]}>
                  <MaterialCommunityIcons name="cash-multiple" size={20} color="#1976D2" />
                </View>
              </View>
              <Text style={styles.overviewCardTitle}>Total Sales</Text>
              <Text style={styles.overviewCardValue}>₹{analytics.totalOverallSales.toFixed(0)}</Text>
              <Text style={styles.overviewCardSubtitle}>{analytics.overallBillCount} transactions</Text>
            </View>

            <View style={styles.overviewCard}>
              <View style={styles.overviewCardHeader}>
                <View style={[styles.overviewIconContainer, { backgroundColor: '#E8F5E8' }]}>
                  <MaterialCommunityIcons name="calendar-month" size={20} color="#2E7D32" />
                </View>
              </View>
              <Text style={styles.overviewCardTitle}>This Month</Text>
              <Text style={styles.overviewCardValue}>₹{analytics.totalMonthlySales.toFixed(0)}</Text>
              <Text style={styles.overviewCardSubtitle}>{analytics.monthlyBillCount} bills</Text>
            </View>

            <View style={styles.overviewCard}>
              <View style={styles.overviewCardHeader}>
                <View style={[styles.overviewIconContainer, { backgroundColor: '#FFF3E0' }]}>
                  <MaterialCommunityIcons name="calendar-week" size={20} color="#F57C00" />
                </View>
              </View>
              <Text style={styles.overviewCardTitle}>This Week</Text>
              <Text style={styles.overviewCardValue}>₹{analytics.totalWeeklySales.toFixed(0)}</Text>
              <Text style={styles.overviewCardSubtitle}>{analytics.weeklyBillCount} bills</Text>
            </View>

            <View style={styles.overviewCard}>
              <View style={styles.overviewCardHeader}>
                <View style={[styles.overviewIconContainer, { backgroundColor: '#F3E5F5' }]}>
                  <MaterialCommunityIcons name="scale-balance" size={20} color="#7B1FA2" />
                </View>
              </View>
              <Text style={styles.overviewCardTitle}>Avg. Bill</Text>
              <Text style={styles.overviewCardValue}>₹{analytics.averageBillAmount.toFixed(0)}</Text>
              <Text style={styles.overviewCardSubtitle}>per transaction</Text>
            </View>
          </View>
        </AnimatedView>
        {/* Sales Chart */}
        <AnimatedView entering={FadeInUp.delay(300).duration(600)} style={styles.chartSection}>
          <View style={styles.chartCard}>
            <View style={styles.chartHeader}>
              <Text style={styles.chartTitle}>Sales Trend</Text>
              <Text style={styles.chartPeriod}>Last 7 days</Text>
            </View>
            <SalesChart chartData={analytics.salesTrendData} />
          </View>
        </AnimatedView>

        {/* Analytics Grid */}
        <AnimatedView entering={FadeInUp.delay(400).duration(600)} style={styles.analyticsSection}>
          <Text style={styles.sectionTitle}>Performance</Text>
          <View style={styles.analyticsGrid}>
            <View style={styles.analyticsCard}>
              <View style={styles.analyticsCardHeader}>
                <View style={[styles.analyticsIconContainer, { backgroundColor: '#E8F5E8' }]}>
                  <MaterialCommunityIcons name="trending-up" size={24} color="#2E7D32" />
                </View>
                <View style={styles.analyticsCardContent}>
                  <Text style={styles.analyticsCardTitle}>Growth Rate</Text>
                  <Text style={styles.analyticsCardValue}>+12%</Text>
                </View>
              </View>
            </View>

            <View style={styles.analyticsCard}>
              <View style={styles.analyticsCardHeader}>
                <View style={[styles.analyticsIconContainer, { backgroundColor: '#FFF3E0' }]}>
                  <MaterialCommunityIcons name="account-group" size={24} color="#F57C00" />
                </View>
                <View style={styles.analyticsCardContent}>
                  <Text style={styles.analyticsCardTitle}>Customers</Text>
                  <Text style={styles.analyticsCardValue}>{analytics.overallBillCount}</Text>
                </View>
              </View>
            </View>
          </View>
        </AnimatedView>

        {/* Popular Items */}
        <AnimatedView entering={FadeInUp.delay(500).duration(600)} style={styles.popularItemsSection}>
          <View style={styles.popularItemsCard}>
            <View style={styles.chartHeader}>
              <Text style={styles.chartTitle}>Popular Items</Text>
              <Text style={styles.chartPeriod}>Top 5</Text>
            </View>
            {analytics.popularItems.length > 0 ? (
              analytics.popularItems.slice(0, 5).map((item, index) => (
                <View key={item.name} style={[styles.popularItemRow, index === analytics.popularItems.length - 1 && { borderBottomWidth: 0 }]}>
                  <View style={styles.popularItemRank}>
                    <Text style={styles.popularItemRankText}>{index + 1}</Text>
                  </View>
                  <View style={styles.popularItemInfo}>
                    <Text style={styles.popularItemName}>{item.name}</Text>
                    <Text style={styles.popularItemCount}>{item.count} sold</Text>
                  </View>
                </View>
              ))
            ) : (
              <View style={{ alignItems: 'center', paddingVertical: 20 }}>
                <MaterialCommunityIcons name="package-variant" size={32} color={theme.colors.onSurfaceVariant} />
                <Text style={{ color: theme.colors.onSurfaceVariant, marginTop: 8 }}>No items data available</Text>
              </View>
            )}
          </View>
        </AnimatedView>

        {/* Quick Tips */}
        <AnimatedView entering={FadeInUp.delay(600).duration(600)} style={styles.popularItemsSection}>
          <View style={[styles.popularItemsCard, { backgroundColor: theme.colors.tertiaryContainer }]}>
            <View style={styles.chartHeader}>
              <Text style={[styles.chartTitle, { color: theme.colors.onTertiaryContainer }]}>Business Tips</Text>
              <MaterialCommunityIcons name="lightbulb-outline" size={20} color={theme.colors.onTertiaryContainer} />
            </View>
            <QuickTips />
          </View>
        </AnimatedView>
      </ScrollView>
    </SafeAreaView>
  );
}