import { OfflineNotice } from '@/components/ui/OfflineNotice';
// import { useColorScheme } from '@/hooks/useColorScheme'; // Commented out or remove if not used elsewhere
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Drawer } from 'expo-router/drawer';
import React from 'react';
import { useTheme } from 'react-native-paper';

export default function DrawerLayout() {
  const theme = useTheme();
  // const colorScheme = useColorScheme(); // Removed unused variable

  return (
    <>
      <OfflineNotice />
      <Drawer
        screenOptions={{
          drawerActiveTintColor: theme.colors.primary,
          drawerStyle: {
            backgroundColor: theme.colors.background,
          },
          headerShown: false,
        }}
      >
        <Drawer.Screen
          name="index"
          options={{
            title: 'Home',
            drawerIcon: ({ color, size }) => <MaterialCommunityIcons name="home" color={color} size={size} />,
          }}
        />
        <Drawer.Screen
          name="new-bill"
          options={{
            title: 'Quick Bill',
            drawerIcon: ({ color, size }) => <MaterialCommunityIcons name="receipt" color={color} size={size} />,
          }}
        />
        <Drawer.Screen
          name="history"
          options={{
            title: 'History',
            drawerIcon: ({ color, size }) => <MaterialCommunityIcons name="history" color={color} size={size} />,
          }}
        />
        <Drawer.Screen
          name="dashboard"
          options={{
            title: 'Dashboard',
            drawerIcon: ({ color, size }) => <MaterialCommunityIcons name="view-dashboard" color={color} size={size} />,
          }}
        />
        <Drawer.Screen
          name="settings"
          options={{
            title: 'Settings',
            drawerIcon: ({ color, size }) => <MaterialCommunityIcons name="cog" color={color} size={size} />,
          }}
        />
      </Drawer>
    </>
  );
}
