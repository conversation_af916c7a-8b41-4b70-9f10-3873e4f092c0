import { useColorScheme } from '@/hooks/useColorScheme';
import { Bill, BillItem } from '@/types/bill';
import { clearAllBills, getBills } from '@/utils/storage';
import { scheduleDailySalesNotificationAsync, cancelDailySalesNotificationAsync, requestNotificationPermissionsAsync } from '@/utils/notificationUtils';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useState } from 'react';
import { Alert, SafeAreaView, ScrollView, StyleSheet, View, TouchableOpacity, Platform } from 'react-native';
import {
    ActivityIndicator,
    Button,
    Card,
    Chip,
    Dialog,
    Divider,
    List,
    Portal,
    RadioButton,
    SegmentedButtons,
    Switch,
    Text,
    TextInput,
    TouchableRipple,
    useTheme
} from 'react-native-paper';
import { DatePickerModal, en, registerTranslation } from 'react-native-paper-dates';
import { useRouter, useNavigation } from 'expo-router';
import { useThemeContext } from '../ThemeContext';

registerTranslation('en', en);

type ExportFormat = 'csv' | 'json';

const isValidBill = (bill: any): bill is Bill => {
  return bill &&
         typeof bill.id === 'string' &&
         typeof bill.date === 'string' &&
         typeof bill.totalAmount === 'number' &&
         typeof bill.name === 'string' &&
         Array.isArray(bill.items) &&
         bill.items.every((item: any) =>
           item &&
           (item.name === undefined || typeof item.name === 'string') &&
           typeof item.quantity === 'number' &&
           typeof item.price === 'number' &&
           typeof item.amount === 'number'
         );
};

export default function SettingsScreen() {
  const themeFromPaper = useTheme();
  const { theme, activeTheme, isLoadingTheme, setTheme } = useThemeContext();
  const colorScheme = useColorScheme();
  const router = useRouter();
  const navigation = useNavigation();
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [dateRange, setDateRange] = useState<{
    startDate: Date | undefined;
    endDate: Date | undefined;
  }>({
    startDate: undefined,
    endDate: undefined,
  });
  const [clearDialogVisible, setClearDialogVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [isClearing, setIsClearing] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const [billsCount, setBillsCount] = useState(0);
  const [exportFormat, setExportFormat] = useState<ExportFormat>('csv');
  const [selectedExportOption, setSelectedExportOption] = useState('all');
  const [appLockEnabled, setAppLockEnabled] = useState(false);
  const [feedbackDialogVisible, setFeedbackDialogVisible] = useState(false);
  const [feedbackText, setFeedbackText] = useState('');
  const [feedbackRating, setFeedbackRating] = useState(5);
  const [dailySummaryNotification, setDailySummaryNotification] = useState(false);

  const openDrawer = () => {
    (navigation as any).openDrawer();
  };

  const loadOtherSettings = useCallback(async () => {
    setIsLoading(true);
    try {
      const bills = await getBills();
      setBillsCount(bills.length);

      const storedAppLock = await AsyncStorage.getItem('gift_corner_app_lock');
      if (storedAppLock) setAppLockEnabled(storedAppLock === 'true');

      const storedDailySummary = await AsyncStorage.getItem('gift_corner_daily_summary_notification');
      if (storedDailySummary) setDailySummaryNotification(storedDailySummary === 'true');
    } catch (error) {
      console.error('Error loading non-theme settings:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadOtherSettings();
  }, [loadOtherSettings]);

  const handleToggleTheme = async () => {
    if (theme === 'system') {
      await setTheme(activeTheme === 'light' ? 'dark' : 'light');
    } else {
      await setTheme(theme === 'light' ? 'dark' : 'light');
    }
  };

  const handleToggleDailySummaryNotification = async (value: boolean) => {
    setDailySummaryNotification(value);
    await AsyncStorage.setItem('gift_corner_daily_summary_notification', value.toString());

    if (value) {
      const hasPermission = await requestNotificationPermissionsAsync();
      if (hasPermission) {
        await scheduleDailySalesNotificationAsync();
        Alert.alert("Daily Summary Enabled", "You'll receive a notification with your daily sales summary at 9 PM.");
      } else {
        setDailySummaryNotification(false);
        await AsyncStorage.setItem('gift_corner_daily_summary_notification', 'false');
        Alert.alert("Permission Required", "Please enable notifications in your device settings to receive daily summaries.");
      }
    } else {
      await cancelDailySalesNotificationAsync();
      Alert.alert("Daily Summary Disabled", "You won't receive daily sales notifications anymore.");
    }
  };

  const escapeCSV = (value: string | undefined): string => {
    const strValue = String(value || '');
    if (strValue.includes('"') || strValue.includes(',') || strValue.includes('\n')) {
      return `"${strValue.replace(/"/g, '""')}"`;
    }
    return `"${strValue}"`;
  };

  const convertToCSV = (bills: Bill[]) => {
    if (bills.length === 0) return '';

    const header = [
      'Bill ID', 'Date', 'Customer Name',
      'Subtotal', 'Discount Amount', 'Final Amount',
      'Item Name', 'Item Quantity', 'Item Price', 'Item Total'
    ].map(h => escapeCSV(h)).join(',');

    const rows = bills.flatMap(bill => {
      if (bill.items.length === 0) {
        return [
          escapeCSV(bill.id),
          escapeCSV(new Date(bill.date).toLocaleString()),
          escapeCSV(bill.name || ''),
          escapeCSV(bill.totalAmount.toFixed(2)),
          escapeCSV((bill.discountAmount || 0).toFixed(2)),
          escapeCSV((bill.finalAmount || bill.totalAmount).toFixed(2)),
          escapeCSV(''),
          escapeCSV('0'),
          escapeCSV('0.00'),
          escapeCSV('0.00')
        ].join(',');
      }
      return bill.items.map(item => [
        escapeCSV(bill.id),
        escapeCSV(new Date(bill.date).toLocaleString()),
        escapeCSV(bill.name || ''),
        escapeCSV(bill.totalAmount.toFixed(2)),
        escapeCSV((bill.discountAmount || 0).toFixed(2)),
        escapeCSV((bill.finalAmount || bill.totalAmount).toFixed(2)),
        escapeCSV(item.name || ''),
        escapeCSV(item.quantity.toString()),
        escapeCSV(item.price.toFixed(2)),
        escapeCSV(item.amount.toFixed(2))
      ].join(','));
    });

    return [header, ...rows].join('\n');
  };

  const handleExportBills = async () => {
    setIsExporting(true);
    try {
      const bills = await getBills();

      if (bills.length === 0) {
        Alert.alert('No Bills', 'There are no bills to export.');
        return;
      }

      let filteredBills = bills;

      if (selectedExportOption === 'date_range' && (dateRange.startDate || dateRange.endDate)) {
        filteredBills = bills.filter(bill => {
          const billDate = new Date(bill.date);
          let isIncluded = true;

          if (dateRange.startDate) {
            const startDay = new Date(dateRange.startDate);
            startDay.setHours(0, 0, 0, 0);
            isIncluded = isIncluded && billDate >= startDay;
          }

          if (dateRange.endDate) {
            const endDay = new Date(dateRange.endDate);
            endDay.setHours(23, 59, 59, 999);
            isIncluded = isIncluded && billDate <= endDay;
          }

          return isIncluded;
        });
      }

      if (filteredBills.length === 0) {
        Alert.alert('No Bills', 'No bills found in the selected date range.');
        return;
      }

      let filename = FileSystem.documentDirectory + 'gift_corner_bills';
      if (selectedExportOption === 'date_range' && dateRange.startDate && dateRange.endDate) {
        const startStr = dateRange.startDate.toISOString().split('T')[0];
        const endStr = dateRange.endDate.toISOString().split('T')[0];
        filename += `_${startStr}_to_${endStr}`;
      }

      let data: string;
      let mimeType: string;

      if (exportFormat === 'csv') {
        filename += '.csv';
        data = convertToCSV(filteredBills);
        mimeType = 'text/csv';
      } else {
        filename += '.json';
        data = JSON.stringify(filteredBills, null, 2);
        mimeType = 'application/json';
      }

      await FileSystem.writeAsStringAsync(filename, data, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      if (!(await Sharing.isAvailableAsync())) {
        Alert.alert(
          'Sharing Not Available',
          'Sharing is not available on this device. The file has been saved to your documents folder.'
        );
        setIsExporting(false);
        return;
      }

      await Sharing.shareAsync(filename, {
        mimeType: mimeType,
        dialogTitle: `Export Bills as ${exportFormat.toUpperCase()}`,
      });
    } catch (error) {
      console.error('Error exporting bills:', error);
      Alert.alert('Export Error', 'Could not export bills.');
    } finally {
      setIsExporting(false);
      setDateRange({ startDate: undefined, endDate: undefined });
      setSelectedExportOption('all');
    }
  };

  const handleBackupData = async () => {
    setIsBackingUp(true);
    try {
      const bills = await getBills();
      const settingsToBackup = {
        theme: await AsyncStorage.getItem('gift_corner_theme'),
        showItemName: await AsyncStorage.getItem('gift_corner_show_item_name_preference'),
        currency: await AsyncStorage.getItem('gift_corner_currency'),
        roundPrices: await AsyncStorage.getItem('gift_corner_round_prices'),
        appLock: await AsyncStorage.getItem('gift_corner_app_lock'),
        dailySummaryNotification: await AsyncStorage.getItem('gift_corner_daily_summary_notification'),
      };
      const backupData = {
        bills,
        settings: settingsToBackup,
        backupDate: new Date().toISOString(),
        appName: 'GiftCornerBackup',
        version: Constants.expoConfig?.version || 'unknown'
      };

      const filename = FileSystem.documentDirectory + `gift_corner_backup_${new Date().toISOString().split('T')[0]}.json`;
      const data = JSON.stringify(backupData, null, 2);

      await FileSystem.writeAsStringAsync(filename, data, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      if (!(await Sharing.isAvailableAsync())) {
        Alert.alert(
          'Sharing Not Available',
          'Sharing is not available on this device. The backup file has been saved to your documents folder.'
        );
        setIsBackingUp(false);
        return;
      }

      await Sharing.shareAsync(filename, {
        mimeType: 'application/json',
        dialogTitle: 'Backup Data',
      });
      Alert.alert('Backup Successful', `Data backed up to ${filename.split('/').pop()}.`);
    } catch (error) {
      console.error('Error backing up data:', error);
      Alert.alert('Backup Error', 'Could not back up data.');
    } finally {
      setIsBackingUp(false);
    }
  };

  const handleRestoreData = async () => {
    setIsRestoring(true);
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'application/json',
        copyToCacheDirectory: true,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        setIsRestoring(false);
        return;
      }

      const fileUri = result.assets[0].uri;
      const fileContent = await FileSystem.readAsStringAsync(fileUri);
      const backupData = JSON.parse(fileContent);

      if (!backupData.appName || backupData.appName !== 'GiftCornerBackup' || !backupData.bills || !backupData.settings) {
        Alert.alert('Invalid Backup File', 'The selected file is not a valid Gift Corner backup file.');
        setIsRestoring(false);
        return;
      }

      if (Array.isArray(backupData.bills)) {
        const validBills = backupData.bills.filter(isValidBill);
        await AsyncStorage.setItem('gift_corner_bills', JSON.stringify(validBills));
      }

      if (backupData.settings.theme) await AsyncStorage.setItem('gift_corner_theme', backupData.settings.theme);
      if (backupData.settings.showItemName) await AsyncStorage.setItem('gift_corner_show_item_name_preference', backupData.settings.showItemName);
      if (backupData.settings.currency) await AsyncStorage.setItem('gift_corner_currency', backupData.settings.currency);
      if (backupData.settings.roundPrices) await AsyncStorage.setItem('gift_corner_round_prices', backupData.settings.roundPrices);
      if (backupData.settings.appLock) await AsyncStorage.setItem('gift_corner_app_lock', backupData.settings.appLock);
      if (backupData.settings.dailySummaryNotification) await AsyncStorage.setItem('gift_corner_daily_summary_notification', backupData.settings.dailySummaryNotification);

      Alert.alert('Restore Successful', 'Data has been restored from the backup.');

      loadOtherSettings();
      const storedTheme = await AsyncStorage.getItem('gift_corner_theme');
      if (storedTheme) {
        setTheme(storedTheme as 'light' | 'dark' | 'system');
      } else {
        setTheme('system');
      }

    } catch (error: any) {
      console.error('Error restoring data:', error);
      Alert.alert('Restore Error', `Could not restore data: ${error.message}`);
    } finally {
      setIsRestoring(false);
    }
  };

  const confirmClearAllData = async () => {
    setIsClearing(true);
    setClearDialogVisible(false);
    try {
      await clearAllBills();
      await AsyncStorage.removeItem('gift_corner_theme');
      await AsyncStorage.removeItem('gift_corner_currency');
      await AsyncStorage.removeItem('gift_corner_round_prices');
      await AsyncStorage.removeItem('gift_corner_app_lock');
      await AsyncStorage.removeItem('gift_corner_daily_summary_notification');
      await AsyncStorage.removeItem('gift_corner_show_item_name_preference');
      Alert.alert('Data Cleared', 'All bills and settings have been cleared.');
      loadOtherSettings();
      setTheme('system');
    } catch (error) {
      console.error('Error clearing data:', error);
      Alert.alert('Error', 'Could not clear all data.');
    } finally {
      setIsClearing(false);
    }
  };

  const handleSubmitFeedback = async () => {
    if (feedbackText.trim() === '') {
      Alert.alert("Feedback Empty", "Please enter your feedback before submitting.");
      return;
    }
    console.log("Feedback Submitted:", { rating: feedbackRating, text: feedbackText });
    setFeedbackDialogVisible(false);
    setFeedbackText('');
    setFeedbackRating(5);
    Alert.alert("Feedback Submitted", "Thank you for your feedback!");
  };

  if (isLoadingTheme || isLoading) {
    return (
      <SafeAreaView style={[styles.safeArea, { backgroundColor: themeFromPaper.colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeFromPaper.colors.primary} />
          <Text style={[styles.loadingText, { color: themeFromPaper.colors.onBackground }]}>Loading Settings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: themeFromPaper.colors.background }]}>
      <StatusBar style={activeTheme === 'dark' ? 'light' : 'dark'} />

      {/* Modern Header */}
      <View style={[styles.modernHeader, { backgroundColor: themeFromPaper.colors.surface }]}>
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={openDrawer} style={[styles.menuButton, { backgroundColor: themeFromPaper.colors.surfaceVariant }]}>
            <MaterialCommunityIcons name="menu" size={24} color={themeFromPaper.colors.onSurface} />
          </TouchableOpacity>

          <View style={styles.headerTitleContainer}>
            <Text style={[styles.headerTitle, { color: themeFromPaper.colors.onSurface }]}>Settings</Text>
            <Text style={[styles.headerSubtitle, { color: themeFromPaper.colors.onSurfaceVariant }]}>Manage your preferences</Text>
          </View>

          <View style={styles.headerActions}>
            <TouchableOpacity style={[styles.profileButton, { backgroundColor: themeFromPaper.colors.surfaceVariant }]}>
              <MaterialCommunityIcons name="account-circle" size={22} color={themeFromPaper.colors.onSurface} />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <ScrollView
        style={[styles.container, { backgroundColor: themeFromPaper.colors.background }]}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Quick Stats Card */}
        <View style={styles.quickStatsSection}>
          <Card style={[styles.quickStatsCard, { backgroundColor: themeFromPaper.colors.surface }]} elevation={2}>
            <Card.Content style={styles.statsCardContent}>
              <View style={styles.statsRow}>
                <View style={styles.statItem}>
                  <View style={[styles.statIconContainer, { backgroundColor: themeFromPaper.colors.primaryContainer }]}>
                    <MaterialCommunityIcons name="receipt" size={24} color={themeFromPaper.colors.onPrimaryContainer} />
                  </View>
                  <Text style={[styles.statValue, { color: themeFromPaper.colors.onSurface }]}>{billsCount}</Text>
                  <Text style={[styles.statLabel, { color: themeFromPaper.colors.onSurfaceVariant }]}>Total Bills</Text>
                </View>
                <View style={[styles.statDivider, { backgroundColor: themeFromPaper.colors.outline }]} />
                <View style={styles.statItem}>
                  <View style={[styles.statIconContainer, { backgroundColor: themeFromPaper.colors.secondaryContainer }]}>
                    <MaterialCommunityIcons name="information" size={24} color={themeFromPaper.colors.onSecondaryContainer} />
                  </View>
                  <Text style={[styles.statValue, { color: themeFromPaper.colors.onSurface }]}>v1.0.0</Text>
                  <Text style={[styles.statLabel, { color: themeFromPaper.colors.onSurfaceVariant }]}>App Version</Text>
                </View>
              </View>
            </Card.Content>
          </Card>
        </View>

        {/* Preferences Section */}
        <View style={styles.settingsSection}>
          <Text style={[styles.sectionTitle, { color: themeFromPaper.colors.onBackground }]}>Preferences</Text>
          <Card style={[styles.settingsCard, { backgroundColor: themeFromPaper.colors.surface }]} elevation={1}>
            <Card.Content style={styles.cardContent}>
              {/* Theme Setting */}
              <View style={styles.settingRow}>
                <View style={styles.settingLeft}>
                  <View style={[styles.settingIconContainer, { backgroundColor: themeFromPaper.colors.primaryContainer }]}>
                    <MaterialCommunityIcons name="theme-light-dark" size={20} color={themeFromPaper.colors.onPrimaryContainer} />
                  </View>
                  <View style={styles.settingInfo}>
                    <Text style={[styles.settingTitle, { color: themeFromPaper.colors.onSurface }]}>Dark Mode</Text>
                    <Text style={[styles.settingSubtitle, { color: themeFromPaper.colors.onSurfaceVariant }]}>Toggle app theme</Text>
                  </View>
                </View>
                <Switch
                  value={activeTheme === 'dark'}
                  onValueChange={handleToggleTheme}
                  thumbColor={themeFromPaper.colors.primary}
                  trackColor={{ false: themeFromPaper.colors.outline, true: themeFromPaper.colors.primaryContainer }}
                />
              </View>

              <Divider style={[styles.settingDivider, { backgroundColor: themeFromPaper.colors.outline }]} />

              {/* Notifications Setting */}
              <View style={styles.settingRow}>
                <View style={styles.settingLeft}>
                  <View style={[styles.settingIconContainer, { backgroundColor: themeFromPaper.colors.secondaryContainer }]}>
                    <MaterialCommunityIcons name="bell" size={20} color={themeFromPaper.colors.onSecondaryContainer} />
                  </View>
                  <View style={styles.settingInfo}>
                    <Text style={[styles.settingTitle, { color: themeFromPaper.colors.onSurface }]}>Daily Summary</Text>
                    <Text style={[styles.settingSubtitle, { color: themeFromPaper.colors.onSurfaceVariant }]}>End of day notifications</Text>
                  </View>
                </View>
                <Switch
                  value={dailySummaryNotification}
                  onValueChange={handleToggleDailySummaryNotification}
                  thumbColor={themeFromPaper.colors.primary}
                  trackColor={{ false: themeFromPaper.colors.outline, true: themeFromPaper.colors.primaryContainer }}
                />
              </View>

              <Divider style={[styles.settingDivider, { backgroundColor: themeFromPaper.colors.outline }]} />

              {/* Printer Setup */}
              <TouchableOpacity
                style={styles.settingRow}
                onPress={() => router.push('/settings/printer-setup')}
              >
                <View style={styles.settingLeft}>
                  <View style={[styles.settingIconContainer, { backgroundColor: themeFromPaper.colors.tertiaryContainer }]}>
                    <MaterialCommunityIcons name="printer" size={20} color={themeFromPaper.colors.onTertiaryContainer} />
                  </View>
                  <View style={styles.settingInfo}>
                    <Text style={[styles.settingTitle, { color: themeFromPaper.colors.onSurface }]}>Printer Setup</Text>
                    <Text style={[styles.settingSubtitle, { color: themeFromPaper.colors.onSurfaceVariant }]}>Configure receipt printer</Text>
                  </View>
                </View>
                <MaterialCommunityIcons name="chevron-right" size={24} color={themeFromPaper.colors.onSurfaceVariant} />
              </TouchableOpacity>
            </Card.Content>
          </Card>
        </View>

        {/* Data Management Section */}
        <View style={styles.settingsSection}>
          <Text style={[styles.sectionTitle, { color: themeFromPaper.colors.onBackground }]}>Data Management</Text>
          <Card style={[styles.settingsCard, { backgroundColor: themeFromPaper.colors.surface }]} elevation={1}>
            <Card.Content style={styles.cardContent}>
              {/* Backup Data */}
              <TouchableOpacity
                style={styles.settingRow}
                onPress={handleBackupData}
                disabled={isBackingUp}
              >
                <View style={styles.settingLeft}>
                  <View style={[styles.settingIconContainer, { backgroundColor: themeFromPaper.colors.primaryContainer }]}>
                    <MaterialCommunityIcons name="cloud-upload" size={20} color={themeFromPaper.colors.onPrimaryContainer} />
                  </View>
                  <View style={styles.settingInfo}>
                    <Text style={[styles.settingTitle, { color: themeFromPaper.colors.onSurface }]}>Backup Data</Text>
                    <Text style={[styles.settingSubtitle, { color: themeFromPaper.colors.onSurfaceVariant }]}>Save bills and settings</Text>
                  </View>
                </View>
                {isBackingUp ? (
                  <ActivityIndicator size="small" color={themeFromPaper.colors.primary} />
                ) : (
                  <MaterialCommunityIcons name="chevron-right" size={24} color={themeFromPaper.colors.onSurfaceVariant} />
                )}
              </TouchableOpacity>

              <Divider style={[styles.settingDivider, { backgroundColor: themeFromPaper.colors.outline }]} />

              {/* Restore Data */}
              <TouchableOpacity
                style={styles.settingRow}
                onPress={handleRestoreData}
                disabled={isRestoring}
              >
                <View style={styles.settingLeft}>
                  <View style={[styles.settingIconContainer, { backgroundColor: themeFromPaper.colors.secondaryContainer }]}>
                    <MaterialCommunityIcons name="cloud-download" size={20} color={themeFromPaper.colors.onSecondaryContainer} />
                  </View>
                  <View style={styles.settingInfo}>
                    <Text style={[styles.settingTitle, { color: themeFromPaper.colors.onSurface }]}>Restore Data</Text>
                    <Text style={[styles.settingSubtitle, { color: themeFromPaper.colors.onSurfaceVariant }]}>Import backup file</Text>
                  </View>
                </View>
                {isRestoring ? (
                  <ActivityIndicator size="small" color={themeFromPaper.colors.primary} />
                ) : (
                  <MaterialCommunityIcons name="chevron-right" size={24} color={themeFromPaper.colors.onSurfaceVariant} />
                )}
              </TouchableOpacity>

              <Divider style={[styles.settingDivider, { backgroundColor: themeFromPaper.colors.outline }]} />

              {/* Export Bills */}
              <TouchableOpacity
                style={styles.settingRow}
                onPress={handleExportBills}
                disabled={isExporting}
              >
                <View style={styles.settingLeft}>
                  <View style={[styles.settingIconContainer, { backgroundColor: themeFromPaper.colors.tertiaryContainer }]}>
                    <MaterialCommunityIcons name="file-export" size={20} color={themeFromPaper.colors.onTertiaryContainer} />
                  </View>
                  <View style={styles.settingInfo}>
                    <Text style={[styles.settingTitle, { color: themeFromPaper.colors.onSurface }]}>Export Bills</Text>
                    <Text style={[styles.settingSubtitle, { color: themeFromPaper.colors.onSurfaceVariant }]}>Download as CSV/JSON</Text>
                  </View>
                </View>
                {isExporting ? (
                  <ActivityIndicator size="small" color={themeFromPaper.colors.primary} />
                ) : (
                  <MaterialCommunityIcons name="chevron-right" size={24} color={themeFromPaper.colors.onSurfaceVariant} />
                )}
              </TouchableOpacity>
            </Card.Content>
          </Card>
        </View>

        {/* Support Section */}
        <View style={styles.settingsSection}>
          <Text style={[styles.sectionTitle, { color: themeFromPaper.colors.onBackground }]}>Support</Text>
          <Card style={[styles.settingsCard, { backgroundColor: themeFromPaper.colors.surface }]} elevation={1}>
            <Card.Content style={styles.cardContent}>
              {/* Send Feedback */}
              <TouchableOpacity
                style={styles.settingRow}
                onPress={() => setFeedbackDialogVisible(true)}
              >
                <View style={styles.settingLeft}>
                  <View style={[styles.settingIconContainer, { backgroundColor: themeFromPaper.colors.primaryContainer }]}>
                    <MaterialCommunityIcons name="message-text" size={20} color={themeFromPaper.colors.onPrimaryContainer} />
                  </View>
                  <View style={styles.settingInfo}>
                    <Text style={[styles.settingTitle, { color: themeFromPaper.colors.onSurface }]}>Send Feedback</Text>
                    <Text style={[styles.settingSubtitle, { color: themeFromPaper.colors.onSurfaceVariant }]}>Share your thoughts</Text>
                  </View>
                </View>
                <MaterialCommunityIcons name="chevron-right" size={24} color={themeFromPaper.colors.onSurfaceVariant} />
              </TouchableOpacity>

              <Divider style={[styles.settingDivider, { backgroundColor: themeFromPaper.colors.outline }]} />

              {/* Help Center */}
              <TouchableOpacity
                style={styles.settingRow}
                onPress={() => Alert.alert("Help Center", "Help center functionality coming soon.")}
              >
                <View style={styles.settingLeft}>
                  <View style={[styles.settingIconContainer, { backgroundColor: themeFromPaper.colors.secondaryContainer }]}>
                    <MaterialCommunityIcons name="help-circle" size={20} color={themeFromPaper.colors.onSecondaryContainer} />
                  </View>
                  <View style={styles.settingInfo}>
                    <Text style={[styles.settingTitle, { color: themeFromPaper.colors.onSurface }]}>Help Center</Text>
                    <Text style={[styles.settingSubtitle, { color: themeFromPaper.colors.onSurfaceVariant }]}>Get help and support</Text>
                  </View>
                </View>
                <MaterialCommunityIcons name="chevron-right" size={24} color={themeFromPaper.colors.onSurfaceVariant} />
              </TouchableOpacity>
            </Card.Content>
          </Card>
        </View>

        {/* Danger Zone */}
        <View style={styles.settingsSection}>
          <Text style={[styles.sectionTitle, { color: themeFromPaper.colors.error }]}>Danger Zone</Text>
          <Card style={[styles.settingsCard, { backgroundColor: themeFromPaper.colors.surface, borderColor: themeFromPaper.colors.error, borderWidth: 1 }]} elevation={1}>
            <Card.Content style={styles.cardContent}>
              <TouchableOpacity
                style={styles.settingRow}
                onPress={() => setClearDialogVisible(true)}
                disabled={isClearing}
              >
                <View style={styles.settingLeft}>
                  <View style={[styles.settingIconContainer, { backgroundColor: themeFromPaper.colors.errorContainer }]}>
                    <MaterialCommunityIcons name="delete-sweep" size={20} color={themeFromPaper.colors.onErrorContainer} />
                  </View>
                  <View style={styles.settingInfo}>
                    <Text style={[styles.settingTitle, { color: themeFromPaper.colors.error }]}>Clear All Data</Text>
                    <Text style={[styles.settingSubtitle, { color: themeFromPaper.colors.onSurfaceVariant }]}>Delete all bills and settings</Text>
                  </View>
                </View>
                {isClearing ? (
                  <ActivityIndicator size="small" color={themeFromPaper.colors.error} />
                ) : (
                  <MaterialCommunityIcons name="chevron-right" size={24} color={themeFromPaper.colors.error} />
                )}
              </TouchableOpacity>
            </Card.Content>
          </Card>
        </View>

        {/* About Section */}
        <View style={styles.settingsSection}>
          <Card style={[styles.aboutCard, { backgroundColor: themeFromPaper.colors.surface }]} elevation={1}>
            <Card.Content style={styles.aboutContent}>
              <View style={styles.aboutHeader}>
                <View style={[styles.aboutIcon, { backgroundColor: themeFromPaper.colors.primaryContainer }]}>
                  <MaterialCommunityIcons name="store" size={32} color={themeFromPaper.colors.onPrimaryContainer} />
                </View>
                <View style={styles.aboutInfo}>
                  <Text style={[styles.aboutTitle, { color: themeFromPaper.colors.onSurface }]}>Gift Corner</Text>
                  <Text style={[styles.aboutSubtitle, { color: themeFromPaper.colors.onSurfaceVariant }]}>Billing Application</Text>
                </View>
              </View>
              <Text style={[styles.aboutDescription, { color: themeFromPaper.colors.onSurfaceVariant }]}>
                A modern, efficient billing solution designed to streamline your business operations.
              </Text>
              <View style={styles.aboutFooter}>
                <Text style={[styles.aboutVersion, { color: themeFromPaper.colors.onSurfaceVariant }]}>
                  Made with ❤️ • Version 1.0.0
                </Text>
              </View>
            </Card.Content>
          </Card>
        </View>

        {/* Dialogs */}
        <Portal>
          <Dialog visible={clearDialogVisible} onDismiss={() => setClearDialogVisible(false)} theme={themeFromPaper}>
            <Dialog.Title style={{ color: themeFromPaper.colors.onSurface }}>Confirm Clear Data</Dialog.Title>
            <Dialog.Content>
              <Text style={{ color: themeFromPaper.colors.onSurfaceVariant }}>
                Are you sure you want to clear all bills and reset settings? This action cannot be undone.
              </Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={() => setClearDialogVisible(false)} textColor={themeFromPaper.colors.primary}>
                Cancel
              </Button>
              <Button
                onPress={confirmClearAllData}
                buttonColor={themeFromPaper.colors.error}
                textColor={themeFromPaper.colors.onError}
              >
                Clear Data
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>

        <Portal>
          <Dialog visible={feedbackDialogVisible} onDismiss={() => setFeedbackDialogVisible(false)} style={{ backgroundColor: themeFromPaper.colors.surface }}>
            <Dialog.Title style={{ color: themeFromPaper.colors.onSurface }}>Submit Feedback</Dialog.Title>
            <Dialog.Content>
              <Text style={{ color: themeFromPaper.colors.onSurfaceVariant, marginBottom: 8 }}>
                Rate your experience (1-5):
              </Text>
              <View style={{ flexDirection: 'row', justifyContent: 'space-around', marginBottom: 16 }}>
                {[1, 2, 3, 4, 5].map(star => (
                  <TouchableOpacity key={star} onPress={() => setFeedbackRating(star)}>
                    <MaterialCommunityIcons
                      name={star <= feedbackRating ? "star" : "star-outline"}
                      size={32}
                      color={star <= feedbackRating ? themeFromPaper.colors.primary : themeFromPaper.colors.onSurfaceVariant}
                    />
                  </TouchableOpacity>
                ))}
              </View>
              <TextInput
                label="Your Feedback"
                value={feedbackText}
                onChangeText={setFeedbackText}
                multiline
                numberOfLines={4}
                mode="outlined"
                theme={themeFromPaper}
                style={{ backgroundColor: themeFromPaper.colors.background }}
              />
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={() => setFeedbackDialogVisible(false)} textColor={themeFromPaper.colors.primary}>
                Cancel
              </Button>
              <Button
                onPress={handleSubmitFeedback}
                buttonColor={themeFromPaper.colors.primary}
                textColor={themeFromPaper.colors.onPrimary}
              >
                Submit
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>

      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },

  // Modern Header Styles
  modernHeader: {
    paddingTop: Constants.statusBarHeight + 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  menuButton: {
    padding: 12,
    borderRadius: 16,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 13,
    fontWeight: '500',
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  profileButton: {
    padding: 12,
    borderRadius: 16,
  },

  // Content Styles
  scrollViewContent: {
    paddingBottom: 100,
  },

  // Quick Stats Section
  quickStatsSection: {
    paddingHorizontal: 20,
    paddingTop: 20,
    marginBottom: 24,
  },
  quickStatsCard: {
    borderRadius: 20,
  },
  statsCardContent: {
    paddingVertical: 20,
  },
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    gap: 8,
  },
  statIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
  },
  statLabel: {
    fontSize: 13,
    fontWeight: '500',
  },
  statDivider: {
    width: 1,
    height: 40,
    marginHorizontal: 20,
  },

  // Settings Sections
  settingsSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 12,
    letterSpacing: 0.3,
  },
  settingsCard: {
    borderRadius: 16,
  },
  cardContent: {
    paddingVertical: 8,
  },

  // Setting Rows
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 4,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 13,
    fontWeight: '500',
  },
  settingDivider: {
    height: 1,
    marginLeft: 56,
    marginVertical: 4,
  },

  // About Section
  aboutCard: {
    borderRadius: 20,
  },
  aboutContent: {
    paddingVertical: 24,
  },
  aboutHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  aboutIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  aboutInfo: {
    flex: 1,
  },
  aboutTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  aboutSubtitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  aboutDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  aboutFooter: {
    alignItems: 'center',
  },
  aboutVersion: {
    fontSize: 13,
    fontWeight: '500',
  },
});