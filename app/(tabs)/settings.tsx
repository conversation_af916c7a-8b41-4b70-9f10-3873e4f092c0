import { useColorScheme } from '@/hooks/useColorScheme';
import { Bill, BillItem } from '@/types/bill';
import { clearAllBills, getBills } from '@/utils/storage';
import { scheduleDailySalesNotificationAsync, cancelDailySalesNotificationAsync, requestNotificationPermissionsAsync } from '@/utils/notificationUtils';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useState } from 'react';
import { Alert, SafeAreaView, ScrollView, StyleSheet, View, TouchableOpacity, Platform } from 'react-native';
import {
    ActivityIndicator,
    Button,
    Card,
    Chip,
    Dialog,
    Divider,
    List,
    Portal,
    RadioButton,
    SegmentedButtons,
    Switch,
    Text,
    TextInput,
    TouchableRipple,
    useTheme
} from 'react-native-paper';
import { DatePickerModal, en, registerTranslation } from 'react-native-paper-dates';
import { useRouter, useNavigation } from 'expo-router';
import AboutSection from '@/components/settings/AboutSection';
import AppPreferencesSection from '@/components/settings/AppPreferencesSection';
import DataManagementSection from '@/components/settings/DataManagementSection';
import HelpSupportSection from '@/components/settings/HelpSupportSection';
import { useThemeContext } from '../ThemeContext';

registerTranslation('en', en);

type ExportFormat = 'csv' | 'json';

const isValidBill = (bill: any): bill is Bill => {
  return bill &&
         typeof bill.id === 'string' &&
         typeof bill.date === 'string' &&
         typeof bill.totalAmount === 'number' &&
         typeof bill.name === 'string' &&
         Array.isArray(bill.items) &&
         bill.items.every((item: any) =>
           item &&
           (item.name === undefined || typeof item.name === 'string') &&
           typeof item.quantity === 'number' &&
           typeof item.price === 'number' &&
           typeof item.amount === 'number'
         );
};

export default function SettingsScreen() {
  const themeFromPaper = useTheme();
  const { theme, activeTheme, isLoadingTheme, setTheme } = useThemeContext();
  const colorScheme = useColorScheme();
  const router = useRouter();
  const navigation = useNavigation();
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [dateRange, setDateRange] = useState<{
    startDate: Date | undefined;
    endDate: Date | undefined;
  }>({
    startDate: undefined,
    endDate: undefined,
  });
  const [clearDialogVisible, setClearDialogVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [isClearing, setIsClearing] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const [billsCount, setBillsCount] = useState(0);
  const [exportFormat, setExportFormat] = useState<ExportFormat>('csv');
  const [selectedExportOption, setSelectedExportOption] = useState('all');
  const [appLockEnabled, setAppLockEnabled] = useState(false);
  const [feedbackDialogVisible, setFeedbackDialogVisible] = useState(false);
  const [feedbackText, setFeedbackText] = useState('');
  const [feedbackRating, setFeedbackRating] = useState(5);
  const [dailySummaryNotification, setDailySummaryNotification] = useState(false);

  const openDrawer = () => {
    (navigation as any).openDrawer();
  };

  const loadOtherSettings = useCallback(async () => {
    setIsLoading(true);
    try {
      const bills = await getBills();
      setBillsCount(bills.length);

      const storedAppLock = await AsyncStorage.getItem('gift_corner_app_lock');
      if (storedAppLock) setAppLockEnabled(storedAppLock === 'true');

      const storedDailySummary = await AsyncStorage.getItem('gift_corner_daily_summary_notification');
      if (storedDailySummary) setDailySummaryNotification(storedDailySummary === 'true');
    } catch (error) {
      console.error('Error loading non-theme settings:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadOtherSettings();
  }, [loadOtherSettings]);

  const handleToggleTheme = async () => {
    if (theme === 'system') {
      await setTheme(activeTheme === 'light' ? 'dark' : 'light');
    } else {
      await setTheme(theme === 'light' ? 'dark' : 'light');
    }
  };

  const onDismissDatePicker = () => {
    setDatePickerVisible(false);
  };

  const onConfirmDateRange = (params: { startDate: Date | undefined, endDate: Date | undefined }) => {
    setDatePickerVisible(false);
    setDateRange(params);
  };

  const escapeCSV = (value: string | undefined): string => {
    const strValue = String(value || '');
    if (strValue.includes('"') || strValue.includes(',') || strValue.includes('\n')) {
      return `"${strValue.replace(/"/g, '""')}"`;
    }
    return `"${strValue}"`;
  };

  const convertToCSV = (bills: Bill[]) => {
    if (bills.length === 0) return '';

    const header = [
      'Bill ID', 'Date', 'Customer Name',
      'Subtotal', 'Discount Amount', 'Final Amount',
      'Item Name', 'Item Quantity', 'Item Price', 'Item Total'
    ].map(h => escapeCSV(h)).join(',');

    const rows = bills.flatMap(bill => {
      if (bill.items.length === 0) {
        return [
          escapeCSV(bill.id),
          escapeCSV(new Date(bill.date).toLocaleString()),
          escapeCSV(bill.name || ''),
          escapeCSV(bill.totalAmount.toFixed(2)),
          escapeCSV((bill.discountAmount || 0).toFixed(2)),
          escapeCSV((bill.finalAmount || bill.totalAmount).toFixed(2)),
          escapeCSV(''),
          escapeCSV('0'),
          escapeCSV('0.00'),
          escapeCSV('0.00')
        ].join(',');
      }
      return bill.items.map(item => [
        escapeCSV(bill.id),
        escapeCSV(new Date(bill.date).toLocaleString()),
        escapeCSV(bill.name || ''),
        escapeCSV(bill.totalAmount.toFixed(2)),
        escapeCSV((bill.discountAmount || 0).toFixed(2)),
        escapeCSV((bill.finalAmount || bill.totalAmount).toFixed(2)),
        escapeCSV(item.name || ''),
        escapeCSV(item.quantity.toString()),
        escapeCSV(item.price.toFixed(2)),
        escapeCSV(item.amount.toFixed(2))
      ].join(','));
    });

    return [header, ...rows].join('\n');
  };

  const handleExportBills = async () => {
    setIsExporting(true);
    try {
      const bills = await getBills();

      if (bills.length === 0) {
        Alert.alert('No Bills', 'There are no bills to export.');
        return;
      }

      let filteredBills = bills;

      if (selectedExportOption === 'date_range' && (dateRange.startDate || dateRange.endDate)) {
        filteredBills = bills.filter(bill => {
          const billDate = new Date(bill.date);
          let isIncluded = true;

          if (dateRange.startDate) {
            const startDay = new Date(dateRange.startDate);
            startDay.setHours(0, 0, 0, 0);
            isIncluded = isIncluded && billDate >= startDay;
          }

          if (dateRange.endDate) {
            const endDay = new Date(dateRange.endDate);
            endDay.setHours(23, 59, 59, 999);
            isIncluded = isIncluded && billDate <= endDay;
          }

          return isIncluded;
        });
      }

      if (filteredBills.length === 0) {
        Alert.alert('No Bills', 'No bills found in the selected date range.');
        return;
      }

      let filename = FileSystem.documentDirectory + 'gift_corner_bills';
      if (selectedExportOption === 'date_range' && dateRange.startDate && dateRange.endDate) {
        const startStr = dateRange.startDate.toISOString().split('T')[0];
        const endStr = dateRange.endDate.toISOString().split('T')[0];
        filename += `_${startStr}_to_${endStr}`;
      }

      let data: string;
      let mimeType: string;

      if (exportFormat === 'csv') {
        filename += '.csv';
        data = convertToCSV(filteredBills);
        mimeType = 'text/csv';
      } else {
        filename += '.json';
        data = JSON.stringify(filteredBills, null, 2);
        mimeType = 'application/json';
      }

      await FileSystem.writeAsStringAsync(filename, data, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      if (!(await Sharing.isAvailableAsync())) {
        Alert.alert(
          'Sharing Not Available',
          'Sharing is not available on this device. The file has been saved to your documents folder.'
        );
        setIsExporting(false);
        return;
      }

      await Sharing.shareAsync(filename, {
        mimeType: mimeType,
        dialogTitle: `Export Bills as ${exportFormat.toUpperCase()}`,
      });
    } catch (error) {
      console.error('Error exporting bills:', error);
      Alert.alert('Export Error', 'Could not export bills.');
    } finally {
      setIsExporting(false);
      setDateRange({ startDate: undefined, endDate: undefined });
      setSelectedExportOption('all');
    }
  };

  const handleImportBills = async () => {
      setIsImporting(true);
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/json', 'text/csv'],
        copyToCacheDirectory: true,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        setIsImporting(false);
        return;
      }

      const fileUri = result.assets[0].uri;
      const fileContent = await FileSystem.readAsStringAsync(fileUri);
      const fileType = result.assets[0].mimeType;

      let importedBills: Bill[] = [];

      if (fileType === 'application/json') {
        const parsedData = JSON.parse(fileContent);
        if (Array.isArray(parsedData)) {
          importedBills = parsedData.filter(isValidBill);
        } else if (isValidBill(parsedData)) {
          importedBills = [parsedData];
        } else {
          throw new Error('Invalid JSON format. Expected an array of bills or a single bill object.');
        }
      } else if (fileType === 'text/csv' || (fileType === 'application/octet-stream' && fileUri.endsWith('.csv'))) {
        const lines = fileContent.split(/\r\n|\n/);
        if (lines.length < 2) throw new Error('Invalid CSV file: Not enough lines.');

        const headerLine = lines[0].startsWith('\ufeff') ? lines[0].substring(1) : lines[0];
        const headers = headerLine.split(',').map(h => h.replace(/^"|"$/g, '').replace(/""/g, '"').trim());
        const billMap = new Map<string, Bill>();

        for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim() === '') continue;
            const values = lines[i].split(',').map(v => v.replace(/^"|"$/g, '').replace(/""/g, '"').trim());
            const billData: Record<string, string> = {};
            headers.forEach((header, index) => {
                billData[header] = values[index];
            });

            const billId = billData['Bill ID'];
            if (!billId) continue;

            if (!billMap.has(billId)) {
                const newBill: Omit<Bill, 'items'> & { items: BillItem[] } = {
                    id: billId,
                    date: new Date(billData['Date']).toISOString(),
                    name: billData['Customer Name'] || '',
                    totalAmount: parseFloat(billData['Subtotal'] || '0'),
                    discountAmount: parseFloat(billData['Discount Amount'] || '0'),
                    finalAmount: parseFloat(billData['Final Amount'] || '0'),
                    items: [],
                };
                billMap.set(billId, newBill as Bill);
            }

            const bill = billMap.get(billId)!;
            if (billData['Item Name']) {
                bill.items.push({
                    name: billData['Item Name'],
                    quantity: parseInt(billData['Item Quantity'] || '0', 10),
                    price: parseFloat(billData['Item Price'] || '0'),
                    amount: parseFloat(billData['Item Total'] || '0'),
                });
            }
        }
        importedBills = Array.from(billMap.values()).filter(isValidBill);

      } else {
        throw new Error(`Unsupported file type: ${fileType}. Please select a JSON or CSV file.`);
      }

      if (importedBills.length === 0) {
        Alert.alert('No Valid Bills Found', 'The selected file does not contain any valid bill data or is improperly formatted.');
        setIsImporting(false);
        return;
      }

      const existingBills = await getBills();
      const newBills = importedBills.filter(ib => !existingBills.some(eb => eb.id === ib.id));

      if (newBills.length === 0) {
        Alert.alert('No New Bills', 'All bills from the file already exist in the app.');
      } else {
        await AsyncStorage.setItem('gift_corner_bills', JSON.stringify([...existingBills, ...newBills]));
        Alert.alert('Import Successful', `${newBills.length} new bill(s) imported successfully.`);
        loadOtherSettings();
                }

    } catch (error: any) {
      console.error('Error importing bills:', error);
      Alert.alert('Import Error', `Could not import bills: ${error.message}`);
    } finally {
      setIsImporting(false);
    }
  };

  const handleClearAllData = () => {
    setClearDialogVisible(true);
  };

  const confirmClearAllData = async () => {
    setIsClearing(true);
    setClearDialogVisible(false);
    try {
      await clearAllBills();
      await AsyncStorage.removeItem('gift_corner_theme');
      await AsyncStorage.removeItem('gift_corner_currency');
      await AsyncStorage.removeItem('gift_corner_round_prices');
      await AsyncStorage.removeItem('gift_corner_app_lock');
      await AsyncStorage.removeItem('gift_corner_daily_summary_notification');
      await AsyncStorage.removeItem('gift_corner_show_item_name_preference');
      Alert.alert('Data Cleared', 'All bills and settings have been cleared.');
      loadOtherSettings();
      setTheme('system');
    } catch (error) {
      console.error('Error clearing data:', error);
      Alert.alert('Error', 'Could not clear all data.');
    } finally {
      setIsClearing(false);
    }
  };

  const handleToggleAppLock = async (value: boolean) => {
    setAppLockEnabled(value);
    await AsyncStorage.setItem('gift_corner_app_lock', value.toString());
    Alert.alert("App Lock", value ? "App lock enabled (PIN setup required - placeholder)." : "App lock disabled.");
  };

  const handleSubmitFeedback = async () => {
    if (feedbackText.trim() === '') {
      Alert.alert("Feedback Empty", "Please enter your feedback before submitting.");
      return;
    }
    console.log("Feedback Submitted:", { rating: feedbackRating, text: feedbackText });
    setFeedbackDialogVisible(false);
      setFeedbackText('');
      setFeedbackRating(5);
    Alert.alert("Feedback Submitted", "Thank you for your feedback!");
  };

  const handleBackupData = async () => {
      setIsBackingUp(true);
    try {
      const bills = await getBills();
      const settingsToBackup = {
        theme: await AsyncStorage.getItem('gift_corner_theme'),
        showItemName: await AsyncStorage.getItem('gift_corner_show_item_name_preference'),
        currency: await AsyncStorage.getItem('gift_corner_currency'),
        roundPrices: await AsyncStorage.getItem('gift_corner_round_prices'),
        appLock: await AsyncStorage.getItem('gift_corner_app_lock'),
        dailySummaryNotification: await AsyncStorage.getItem('gift_corner_daily_summary_notification'),
      };
      const backupData = {
        bills,
        settings: settingsToBackup,
        backupDate: new Date().toISOString(),
        appName: 'GiftCornerBackup',
        version: Constants.expoConfig?.version || 'unknown'
      };

      const filename = FileSystem.documentDirectory + `gift_corner_backup_${new Date().toISOString().split('T')[0]}.json`;
      const data = JSON.stringify(backupData, null, 2);

      await FileSystem.writeAsStringAsync(filename, data, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      if (!(await Sharing.isAvailableAsync())) {
        Alert.alert(
          'Sharing Not Available',
          'Sharing is not available on this device. The backup file has been saved to your documents folder.'
        );
        setIsBackingUp(false);
        return;
      }

      await Sharing.shareAsync(filename, {
        mimeType: 'application/json',
        dialogTitle: 'Backup Data',
      });
      Alert.alert('Backup Successful', `Data backed up to ${filename.split('/').pop()}.`);
    } catch (error) {
      console.error('Error backing up data:', error);
      Alert.alert('Backup Error', 'Could not back up data.');
    } finally {
      setIsBackingUp(false);
    }
  };

  const handleRestoreData = async () => {
      setIsRestoring(true);
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'application/json',
        copyToCacheDirectory: true,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        setIsRestoring(false);
        return;
      }

      const fileUri = result.assets[0].uri;
      const fileContent = await FileSystem.readAsStringAsync(fileUri);
      const backupData = JSON.parse(fileContent);

      if (!backupData.appName || backupData.appName !== 'GiftCornerBackup' || !backupData.bills || !backupData.settings) {
        Alert.alert('Invalid Backup File', 'The selected file is not a valid Gift Corner backup file.');
        setIsRestoring(false);
        return;
      }

      if (Array.isArray(backupData.bills)) {
          const validBills = backupData.bills.filter(isValidBill);
          await AsyncStorage.setItem('gift_corner_bills', JSON.stringify(validBills));
      }

      if (backupData.settings.theme) await AsyncStorage.setItem('gift_corner_theme', backupData.settings.theme);
      if (backupData.settings.showItemName) await AsyncStorage.setItem('gift_corner_show_item_name_preference', backupData.settings.showItemName);
      if (backupData.settings.currency) await AsyncStorage.setItem('gift_corner_currency', backupData.settings.currency);
      if (backupData.settings.roundPrices) await AsyncStorage.setItem('gift_corner_round_prices', backupData.settings.roundPrices);
      if (backupData.settings.appLock) await AsyncStorage.setItem('gift_corner_app_lock', backupData.settings.appLock);
      if (backupData.settings.dailySummaryNotification) await AsyncStorage.setItem('gift_corner_daily_summary_notification', backupData.settings.dailySummaryNotification);

      Alert.alert('Restore Successful', 'Data has been restored from the backup.');

      loadOtherSettings();
      const storedTheme = await AsyncStorage.getItem('gift_corner_theme');
      if (storedTheme) {
        setTheme(storedTheme as 'light' | 'dark' | 'system');
      } else {
        setTheme('system');
      }

    } catch (error: any) {
      console.error('Error restoring data:', error);
      Alert.alert('Restore Error', `Could not restore data: ${error.message}`);
              } finally {
      setIsRestoring(false);
    }
  };

  const handleToggleDailySummaryNotification = async (value: boolean) => {
    setDailySummaryNotification(value);
    await AsyncStorage.setItem('gift_corner_daily_summary_notification', value.toString());
      if (value) {
      const permissionsGranted = await requestNotificationPermissionsAsync();
      if (permissionsGranted) {
          await scheduleDailySalesNotificationAsync();
        Alert.alert("Notifications Enabled", "Daily sales summary notifications have been scheduled.");
        } else {
        Alert.alert("Permission Denied", "Notification permissions are required to schedule daily summaries.");
        setDailySummaryNotification(false);
        await AsyncStorage.setItem('gift_corner_daily_summary_notification', 'false');
        }
      } else {
        await cancelDailySalesNotificationAsync();
      Alert.alert("Notifications Disabled", "Daily sales summary notifications have been cancelled.");
    }
  };

  const getDateRangeText = () => {
    if (dateRange.startDate && dateRange.endDate) {
      return `From ${dateRange.startDate.toLocaleDateString()} to ${dateRange.endDate.toLocaleDateString()}`;
    } else if (dateRange.startDate) {
      return `From ${dateRange.startDate.toLocaleDateString()}`;
    } else if (dateRange.endDate) {
      return `Until ${dateRange.endDate.toLocaleDateString()}`;
    }
    return 'No date range selected';
  };

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: themeFromPaper.colors.background,
      paddingTop: Platform.OS === 'android' ? Constants.statusBarHeight : 0,
    },
    container: {
      flex: 1,
    },
    header: {
      backgroundColor: '#1a1a1a',
      paddingVertical: 12,
      paddingHorizontal: 16,
      flexDirection: 'row',
      alignItems: 'center',
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: '#ffffff',
      marginLeft: 10,
    },
    scrollViewContent: {
      paddingBottom: 20,
    },
    sectionTitle: {
      marginTop: 24,
      marginBottom: 8,
      marginHorizontal: 16,
      fontSize: 18,
      fontWeight: 'bold',
      color: themeFromPaper.colors.onSurfaceVariant,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: themeFromPaper.colors.background,
    },
    divider: {
      height: 1,
      backgroundColor: themeFromPaper.colors.outlineVariant,
      marginVertical: 8,
    },
  });

  if (isLoadingTheme || isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator animating={true} size="large" color={themeFromPaper.colors.primary} />
        <Text style={{ marginTop: 10, color: themeFromPaper.colors.onSurfaceVariant }}>Loading Settings...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style={activeTheme === 'dark' ? 'light' : 'dark'} />

      {/* Modern Header */}
      <View style={styles.modernHeader}>
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={openDrawer} style={styles.menuButton}>
            <MaterialCommunityIcons name="menu" size={24} color={themeFromPaper.colors.onSurface} />
          </TouchableOpacity>

          <View style={styles.headerTitleContainer}>
            <Text style={styles.headerTitle}>Settings</Text>
            <Text style={styles.headerSubtitle}>App Configuration</Text>
          </View>

          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.profileButton}>
              <MaterialCommunityIcons name="cog" size={22} color={themeFromPaper.colors.onSurface} />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
      >
        {/* User Profile Section */}
        <View style={styles.profileSection}>
          <View style={styles.profileCard}>
            <View style={styles.profileHeader}>
              <View style={styles.profileAvatar}>
                <MaterialCommunityIcons name="store" size={32} color="#FFFFFF" />
              </View>
              <View style={styles.profileInfo}>
                <Text style={styles.profileName}>GIFT CORNER</Text>
                <Text style={styles.profileSubtitle}>Billing Application</Text>
              </View>
              <TouchableOpacity style={styles.editProfileButton}>
                <MaterialCommunityIcons name="pencil" size={20} color={themeFromPaper.colors.primary} />
              </TouchableOpacity>
            </View>
            <View style={styles.profileStats}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{billsCount}</Text>
                <Text style={styles.statLabel}>Bills Created</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statValue}>Active</Text>
                <Text style={styles.statLabel}>Status</Text>
              </View>
            </View>
          </View>
        </View>

        {/* App Preferences */}
        <View style={styles.settingsSection}>
          <Text style={styles.sectionTitle}>App Preferences</Text>
          <View style={styles.settingsCard}>
            <TouchableOpacity style={styles.settingRow}>
              <View style={styles.settingLeft}>
                <View style={[styles.settingIcon, { backgroundColor: '#E3F2FD' }]}>
                  <MaterialCommunityIcons name="theme-light-dark" size={20} color="#1976D2" />
                </View>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Dark Mode</Text>
                  <Text style={styles.settingSubtitle}>Toggle app theme</Text>
                </View>
              </View>
              <Switch value={activeTheme === 'dark'} onValueChange={handleToggleTheme} />
            </TouchableOpacity>

            <View style={styles.settingDivider} />

            <TouchableOpacity style={styles.settingRow}>
              <View style={styles.settingLeft}>
                <View style={[styles.settingIcon, { backgroundColor: '#E8F5E8' }]}>
                  <MaterialCommunityIcons name="bell" size={20} color="#2E7D32" />
                </View>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Daily Summary</Text>
                  <Text style={styles.settingSubtitle}>End of day notifications</Text>
                </View>
              </View>
              <Switch value={dailySummaryNotification} onValueChange={handleToggleDailySummaryNotification} />
            </TouchableOpacity>

            <View style={styles.settingDivider} />

            <TouchableOpacity
              style={styles.settingRow}
              onPress={() => router.push('/settings/printer-setup')}
            >
              <View style={styles.settingLeft}>
                <View style={[styles.settingIcon, { backgroundColor: '#FFF3E0' }]}>
                  <MaterialCommunityIcons name="printer" size={20} color="#F57C00" />
                </View>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Printer Setup</Text>
                  <Text style={styles.settingSubtitle}>Configure receipt printer</Text>
                </View>
              </View>
              <MaterialCommunityIcons name="chevron-right" size={24} color={themeFromPaper.colors.onSurfaceVariant} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Data Management */}
        <View style={styles.settingsSection}>
          <Text style={styles.sectionTitle}>Data Management</Text>
          <View style={styles.settingsCard}>
            <TouchableOpacity
              style={styles.settingRow}
              onPress={handleBackupData}
              disabled={isBackingUp}
            >
              <View style={styles.settingLeft}>
                <View style={[styles.settingIcon, { backgroundColor: '#E3F2FD' }]}>
                  <MaterialCommunityIcons name="cloud-upload" size={20} color="#1976D2" />
                </View>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Backup Data</Text>
                  <Text style={styles.settingSubtitle}>Save bills and settings</Text>
                </View>
              </View>
              {isBackingUp ? (
                <ActivityIndicator size="small" color={themeFromPaper.colors.primary} />
              ) : (
                <MaterialCommunityIcons name="chevron-right" size={24} color={themeFromPaper.colors.onSurfaceVariant} />
              )}
            </TouchableOpacity>

            <View style={styles.settingDivider} />

            <TouchableOpacity
              style={styles.settingRow}
              onPress={handleRestoreData}
              disabled={isRestoring}
            >
              <View style={styles.settingLeft}>
                <View style={[styles.settingIcon, { backgroundColor: '#E8F5E8' }]}>
                  <MaterialCommunityIcons name="cloud-download" size={20} color="#2E7D32" />
                </View>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Restore Data</Text>
                  <Text style={styles.settingSubtitle}>Import backup file</Text>
                </View>
              </View>
              {isRestoring ? (
                <ActivityIndicator size="small" color={themeFromPaper.colors.primary} />
              ) : (
                <MaterialCommunityIcons name="chevron-right" size={24} color={themeFromPaper.colors.onSurfaceVariant} />
              )}
            </TouchableOpacity>

            <View style={styles.settingDivider} />

            <TouchableOpacity
              style={styles.settingRow}
              onPress={handleExportBills}
              disabled={isExporting}
            >
              <View style={styles.settingLeft}>
                <View style={[styles.settingIcon, { backgroundColor: '#FFF3E0' }]}>
                  <MaterialCommunityIcons name="file-export" size={20} color="#F57C00" />
                </View>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Export Bills</Text>
                  <Text style={styles.settingSubtitle}>Download as CSV/PDF</Text>
                </View>
              </View>
              {isExporting ? (
                <ActivityIndicator size="small" color={themeFromPaper.colors.primary} />
              ) : (
                <MaterialCommunityIcons name="chevron-right" size={24} color={themeFromPaper.colors.onSurfaceVariant} />
              )}
            </TouchableOpacity>
          </View>
        </View>

        {/* Help & Support */}
        <View style={styles.settingsSection}>
          <Text style={styles.sectionTitle}>Help & Support</Text>
          <View style={styles.settingsCard}>
            <TouchableOpacity
              style={styles.settingRow}
              onPress={() => setFeedbackDialogVisible(true)}
            >
              <View style={styles.settingLeft}>
                <View style={[styles.settingIcon, { backgroundColor: '#F3E5F5' }]}>
                  <MaterialCommunityIcons name="message-text" size={20} color="#7B1FA2" />
                </View>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Send Feedback</Text>
                  <Text style={styles.settingSubtitle}>Share your thoughts</Text>
                </View>
              </View>
              <MaterialCommunityIcons name="chevron-right" size={24} color={themeFromPaper.colors.onSurfaceVariant} />
            </TouchableOpacity>

            <View style={styles.settingDivider} />

            <TouchableOpacity
              style={styles.settingRow}
              onPress={() => Alert.alert("Rate App", "Rate app functionality to be implemented.")}
            >
              <View style={styles.settingLeft}>
                <View style={[styles.settingIcon, { backgroundColor: '#FFF8E1' }]}>
                  <MaterialCommunityIcons name="star" size={20} color="#FFA000" />
                </View>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Rate App</Text>
                  <Text style={styles.settingSubtitle}>Rate us on app store</Text>
                </View>
              </View>
              <MaterialCommunityIcons name="chevron-right" size={24} color={themeFromPaper.colors.onSurfaceVariant} />
            </TouchableOpacity>

            <View style={styles.settingDivider} />

            <TouchableOpacity
              style={styles.settingRow}
              onPress={() => Alert.alert("User Guide", "User guide functionality to be implemented.")}
            >
              <View style={styles.settingLeft}>
                <View style={[styles.settingIcon, { backgroundColor: '#E0F2F1' }]}>
                  <MaterialCommunityIcons name="help-circle" size={20} color="#00695C" />
                </View>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Help Center</Text>
                  <Text style={styles.settingSubtitle}>Get help and support</Text>
                </View>
              </View>
              <MaterialCommunityIcons name="chevron-right" size={24} color={themeFromPaper.colors.onSurfaceVariant} />
            </TouchableOpacity>
          </View>
        </View>

        {/* About */}
        <View style={styles.settingsSection}>
          <Text style={styles.sectionTitle}>About</Text>
          <View style={styles.settingsCard}>
            <View style={styles.settingRow}>
              <View style={styles.settingLeft}>
                <View style={[styles.settingIcon, { backgroundColor: '#E8EAF6' }]}>
                  <MaterialCommunityIcons name="information" size={20} color="#3F51B5" />
                </View>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>App Version</Text>
                  <Text style={styles.settingSubtitle}>1.0.0</Text>
                </View>
              </View>
            </View>

            <View style={styles.settingDivider} />

            <View style={styles.settingRow}>
              <View style={styles.settingLeft}>
                <View style={[styles.settingIcon, { backgroundColor: '#FCE4EC' }]}>
                  <MaterialCommunityIcons name="heart" size={20} color="#C2185B" />
                </View>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Made with ❤️</Text>
                  <Text style={styles.settingSubtitle}>Gift Corner Team</Text>
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Danger Zone */}
        <View style={styles.settingsSection}>
          <Text style={[styles.sectionTitle, { color: '#D32F2F' }]}>Danger Zone</Text>
          <View style={[styles.settingsCard, { borderColor: '#FFCDD2', borderWidth: 1 }]}>
            <TouchableOpacity
              style={styles.settingRow}
              onPress={handleClearAllData}
              disabled={isClearing}
            >
              <View style={styles.settingLeft}>
                <View style={[styles.settingIcon, { backgroundColor: '#FFEBEE' }]}>
                  <MaterialCommunityIcons name="delete-sweep" size={20} color="#D32F2F" />
                </View>
                <View style={styles.settingInfo}>
                  <Text style={[styles.settingTitle, { color: '#D32F2F' }]}>Clear All Data</Text>
                  <Text style={styles.settingSubtitle}>Delete all bills and settings</Text>
                </View>
              </View>
              {isClearing ? (
                <ActivityIndicator size="small" color="#D32F2F" />
              ) : (
                <MaterialCommunityIcons name="chevron-right" size={24} color="#D32F2F" />
              )}
            </TouchableOpacity>
          </View>
        </View>

        <DatePickerModal
          locale="en"
          mode="range"
          visible={datePickerVisible}
          onDismiss={onDismissDatePicker}
          startDate={dateRange.startDate}
          endDate={dateRange.endDate}
          onConfirm={onConfirmDateRange}
        />

        <Portal>
          <Dialog visible={clearDialogVisible} onDismiss={() => setClearDialogVisible(false)} theme={themeFromPaper}>
            <Dialog.Title>Confirm Clear Data</Dialog.Title>
          <Dialog.Content>
              <Text style={{color: themeFromPaper.colors.onSurfaceVariant}}>Are you sure you want to clear all bills and reset settings? This action cannot be undone.</Text>
          </Dialog.Content>
          <Dialog.Actions>
              <Button onPress={() => setClearDialogVisible(false)} textColor={themeFromPaper.colors.primary}>Cancel</Button>
              <Button onPress={confirmClearAllData} buttonColor={themeFromPaper.colors.error} textColor={themeFromPaper.colors.onError}>Clear Data</Button>
          </Dialog.Actions>
        </Dialog>
        </Portal>

        <Portal>
          <Dialog visible={feedbackDialogVisible} onDismiss={() => setFeedbackDialogVisible(false)} style={{ backgroundColor: themeFromPaper.colors.surface }}>
            <Dialog.Title style={{color: themeFromPaper.colors.onSurface}}>Submit Feedback</Dialog.Title>
          <Dialog.Content>
              <Text style={{color: themeFromPaper.colors.onSurfaceVariant, marginBottom: 8}}>Rate your experience (1-5):</Text>
            <View style={{flexDirection: 'row', justifyContent: 'space-around', marginBottom: 16}}>
                {[1, 2, 3, 4, 5].map(star => (
                  <TouchableOpacity key={star} onPress={() => setFeedbackRating(star)}>
                    <MaterialCommunityIcons
                      name={star <= feedbackRating ? "star" : "star-outline"}
                      size={32}
                      color={star <= feedbackRating ? themeFromPaper.colors.primary : themeFromPaper.colors.onSurfaceVariant}
                    />
                  </TouchableOpacity>
              ))}
            </View>
            <TextInput
              label="Your Feedback"
              value={feedbackText}
              onChangeText={setFeedbackText}
              multiline
              numberOfLines={4}
                mode="outlined"
                theme={themeFromPaper}
                style={{backgroundColor: themeFromPaper.colors.background}}
            />
          </Dialog.Content>
          <Dialog.Actions>
              <Button onPress={() => setFeedbackDialogVisible(false)} textColor={themeFromPaper.colors.primary}>Cancel</Button>
              <Button onPress={handleSubmitFeedback} buttonColor={themeFromPaper.colors.primary} textColor={themeFromPaper.colors.onPrimary}>Submit</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  container: {
    flex: 1,
  },

  // Modern Header Styles
  modernHeader: {
    backgroundColor: '#FFFFFF',
    paddingTop: Constants.statusBarHeight + 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  menuButton: {
    padding: 8,
    borderRadius: 12,
    backgroundColor: '#F5F5F5',
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#000000',
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  profileButton: {
    padding: 8,
    borderRadius: 12,
    backgroundColor: '#F5F5F5',
  },

  // Content Styles
  scrollViewContent: {
    paddingBottom: 100,
  },

  // Profile Section
  profileSection: {
    paddingHorizontal: 20,
    paddingTop: 20,
    marginBottom: 24,
  },
  profileCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  profileAvatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#6200EE',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 20,
    fontWeight: '700',
    color: '#000000',
    marginBottom: 4,
  },
  profileSubtitle: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  editProfileButton: {
    padding: 8,
    borderRadius: 12,
    backgroundColor: '#F5F5F5',
  },
  profileStats: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#000000',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
  },
  statDivider: {
    width: 1,
    height: 32,
    backgroundColor: '#E0E0E0',
    marginHorizontal: 20,
  },

  // Settings Sections
  settingsSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#000000',
    marginBottom: 12,
    letterSpacing: 0.3,
  },
  settingsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
  },
  settingDivider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginLeft: 76,
  },

  // Legacy styles for compatibility
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  section: {
    marginBottom: 20,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  rowLabel: {
    fontSize: 16,
  },
  rowValueText: {
    fontSize: 16,
  },
  divider: {
    marginVertical: 16,
  },
});