import AsyncStorage from '@react-native-async-storage/async-storage';
import { Bill } from '../types/bill';

const BILLS_STORAGE_KEY = 'gift_corner_bills';
// const BILLS_STORAGE_VERSION = 'v1'; // For future migrations if needed
const SHOW_ITEM_NAME_SETTING_KEY = 'gift_corner_show_item_name_setting';

// In-memory cache to improve performance - initialize as empty array instead of null
let billsCache: Bill[] = [];
let isCacheInitialized = false;

/**
 * Get bills with optimized caching
 */
export const getBills = async (): Promise<Bill[]> => {
  try {
    // Return from cache if already initialized
    if (isCacheInitialized) {
      return [...billsCache]; // Return a copy to prevent mutation
    }

    // Otherwise load from storage
    const billsJSON = await AsyncStorage.getItem(BILLS_STORAGE_KEY);
    
    // Update cache
    billsCache = billsJSON ? JSON.parse(billsJSON) : [];
    isCacheInitialized = true;
    
    return [...billsCache];
  } catch (error) {
    console.error('Error getting bills:', error);
    // Return empty array on error to ensure app works offline
    return [];
  }
};

/**
 * Save a bill with optimized performance
 */
export const saveBill = async (bill: Bill): Promise<void> => {
  try {
    // Get existing bills (from cache if possible)
    const existingBills = isCacheInitialized ? [...billsCache] : await getBills();
    
    // Add new bill
    const updatedBills = [...existingBills, bill];
    
    // Update cache first for immediate UI updates
    billsCache = updatedBills;
    isCacheInitialized = true;
    
    // Then persist to storage (don't block UI but still handle errors)
    try {
      await AsyncStorage.setItem(BILLS_STORAGE_KEY, JSON.stringify(updatedBills));
    } catch (storageError) {
      console.error('Error saving bill to storage:', storageError);
      // Consider retrying or showing a notification if critical
    }
  } catch (error) {
    console.error('Error saving bill:', error);
    throw error;
  }
};

/**
 * Delete a bill with optimized performance
 */
export const deleteBill = async (billId: string): Promise<void> => {
  try {
    // Use cache if available, otherwise fetch
    const existingBills = isCacheInitialized ? [...billsCache] : await getBills();
    
    const updatedBills = existingBills.filter(bill => bill.id !== billId);
    
    // Update cache first
    billsCache = updatedBills;
    
    // Persist to storage with proper error handling
    try {
      await AsyncStorage.setItem(BILLS_STORAGE_KEY, JSON.stringify(updatedBills));
    } catch (storageError) {
      console.error('Error deleting bill from storage:', storageError);
      // Consider retrying or showing a notification if critical
    }
  } catch (error) {
    console.error('Error deleting bill:', error);
    throw error;
  }
};

/**
 * Update an existing bill
 */
export const updateBill = async (updatedBill: Bill): Promise<void> => {
  try {
    // Use cache if available, otherwise fetch
    const existingBills = isCacheInitialized ? [...billsCache] : await getBills();
    
    const billIndex = existingBills.findIndex(bill => bill.id === updatedBill.id);
    if (billIndex === -1) {
      throw new Error(`Bill with ID ${updatedBill.id} not found`);
    }
    
    // Create updated bills array
    const updatedBills = [...existingBills];
    updatedBills[billIndex] = updatedBill;
    
    // Update cache first for immediate UI updates
    billsCache = updatedBills;
    
    // Persist to storage with proper error handling
    try {
      await AsyncStorage.setItem(BILLS_STORAGE_KEY, JSON.stringify(updatedBills));
    } catch (storageError) {
      console.error('Error updating bill in storage:', storageError);
      // Consider retrying or showing a notification if critical
    }
  } catch (error) {
    console.error('Error updating bill:', error);
    throw error;
  }
};

/**
 * Clear all bills from storage
 */
export const clearAllBills = async (): Promise<void> => {
  try {
    // Clear cache first
    billsCache = [];
    isCacheInitialized = false;
    
    // Remove from storage
    await AsyncStorage.removeItem(BILLS_STORAGE_KEY);
  } catch (error) {
    console.error('Error clearing bills:', error);
    throw error;
  }
};

/**
 * Clear cache to force reload from storage
 */
export const clearCache = (): void => {
  billsCache = [];
  isCacheInitialized = false;
};

/**
 * Saves the user's preference for showing item name input in the new bill screen.
 * @param value boolean - true to show item name input, false to hide.
 */
export const saveShowItemNameSetting = async (value: boolean): Promise<void> => {
  try {
    await AsyncStorage.setItem(SHOW_ITEM_NAME_SETTING_KEY, JSON.stringify(value));
  } catch (error) {
    console.error('Error saving show item name setting:', error);
    throw error; // Rethrow to allow caller to handle or display message
  }
};

/**
 * Loads the user's preference for showing item name input.
 * @returns Promise<boolean> - true if item name input should be shown, false otherwise. Defaults to false.
 */
export const loadShowItemNameSetting = async (): Promise<boolean> => {
  try {
    const valueJSON = await AsyncStorage.getItem(SHOW_ITEM_NAME_SETTING_KEY);
    if (valueJSON !== null) {
      return JSON.parse(valueJSON);
    }
    return false; // Default to false if no setting is found
  } catch (error) {
    console.error('Error loading show item name setting:', error);
    return false; // Default to false on error
  }
}; 