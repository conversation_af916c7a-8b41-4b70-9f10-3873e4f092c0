import React, { memo, useRef, useEffect, useState, useCallback } from 'react';
import { InteractionManager } from 'react-native';
import isEqual from 'lodash.isequal';

/**
 * Custom memoization function with deep comparison
 * Use for components with complex props that React.memo's shallow comparison would miss
 * 
 * @param {React.Component} Component - The component to memoize
 * @param {Function} propsAreEqual - Optional custom comparison function
 */
export const memoDeep = (Component, propsAreEqual = isEqual) => {
  return memo(Component, propsAreEqual);
};

/**
 * Custom memoization function with comparison by specified keys
 * Only re-renders when the specified prop keys change
 * 
 * @param {React.Component} Component - The component to memoize
 * @param {Array<string>} keys - The prop keys to compare
 */
export const memoWithKeys = (Component, keys = []) => {
  return memo(Component, (prevProps, nextProps) => {
    // If no keys specified, compare all props
    if (!keys.length) {
      return isEqual(prevProps, nextProps);
    }
    
    // Compare only the specified keys
    return keys.every(key => isEqual(prevProps[key], nextProps[key]));
  });
};

/**
 * Higher-order component that prevents updates during animations or interactions
 * Delays rendering until after interactions are complete
 * 
 * @param {React.Component} Component - The component to wrap
 * @param {number} delayMs - Optional additional delay in ms
 */
export const withInteractionDelay = (Component, delayMs = 0) => {
  return (props) => {
    const [readyToRender, setReadyToRender] = useState(false);
    const queuedPropsRef = useRef(props);
    
    useEffect(() => {
      queuedPropsRef.current = props;
      
      if (!readyToRender) return;
      
      const handle = InteractionManager.runAfterInteractions(() => {
        setTimeout(() => {
          setReadyToRender(true);
        }, delayMs);
      });
      
      return () => handle.cancel();
    }, [props, delayMs, readyToRender]);
    
    useEffect(() => {
      const handle = InteractionManager.runAfterInteractions(() => {
        setTimeout(() => {
          setReadyToRender(true);
        }, delayMs);
      });
      
      return () => handle.cancel();
    }, [delayMs]);
    
    if (!readyToRender) {
      return null; // Or a loading placeholder if needed
    }
    
    return <Component {...queuedPropsRef.current} />;
  };
};

/**
 * Hook that only updates a value after interactions are complete
 * Useful for expensive calculations or state updates
 * 
 * @param {any} value - The value to throttle
 * @param {number} delay - Optional delay in ms
 */
export const useInteractionThrottledValue = (value, delay = 0) => {
  const [throttledValue, setThrottledValue] = useState(value);
  const pendingValueRef = useRef(value);
  
  useEffect(() => {
    pendingValueRef.current = value;
    
    const handle = InteractionManager.runAfterInteractions(() => {
      setTimeout(() => {
        setThrottledValue(pendingValueRef.current);
      }, delay);
    });
    
    return () => handle.cancel();
  }, [value, delay]);
  
  return throttledValue;
};

/**
 * Hook to prevent unnecessary re-renders during scrolling
 * Returns true during scrolling and for a short duration after
 */
export const useIsScrolling = (durationAfterScrollMs = 200) => {
  const [isScrolling, setIsScrolling] = useState(false);
  const timeoutRef = useRef(null);
  
  const handleScrollBegin = useCallback(() => {
    setIsScrolling(true);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);
  
  const handleScrollEnd = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, durationAfterScrollMs);
  }, [durationAfterScrollMs]);
  
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  return {
    isScrolling,
    handleScrollBegin,
    handleScrollEnd
  };
};

/**
 * Helper to create an optimized item renderer for FlatList/SectionList
 * 
 * @param {React.Component} ItemComponent - The component to render items
 * @param {Function} getItemType - Function to get item type for more granular control
 */
export const createItemRenderer = (ItemComponent, getItemType = null) => {
  // Memoize the item renderer component
  const MemoizedItem = memo(ItemComponent);
  
  // Return a function that can be passed to renderItem
  return ({ item, index, section, separators }) => {
    // Use the type getter if provided, otherwise use the item itself as the key
    const itemType = getItemType ? getItemType(item) : undefined;
    
    return (
      <MemoizedItem
        item={item}
        index={index}
        section={section}
        separators={separators}
        itemType={itemType}
      />
    );
  };
}; 