// Icon utility to handle mappings of invalid icon names to valid alternatives
import { MaterialCommunityIcons } from '@expo/vector-icons';

// Type for the icon name
export type IconName = keyof typeof MaterialCommunityIcons.glyphMap;

// This is a workaround for TypeScript to accept our string literals in the mapping
type IconStringLiteral = string & {};

// Map of invalid icon names to valid alternatives
const iconMapping: Record<string, IconStringLiteral> = {
  'calendar-all': 'calendar-multiple',
  'calendar-blank-multiple': 'calendar-multiple',
  'receipt-text-plus': 'receipt',
  'receipt-text-check': 'receipt-check-outline',
  // Add more mappings as needed
};

/**
 * Get a valid icon name. If the provided icon name is invalid, returns a suitable alternative.
 * @param name The icon name to validate
 * @returns A valid MaterialCommunityIcons name
 */
export const getValidIconName = (name: string): string => {
  // If the name is valid, return it directly
  if (name in MaterialCommunityIcons.glyphMap) {
    return name;
  }
  
  // If we have a mapping for this invalid name, return the alternative
  if (name in iconMapping) {
    return iconMapping[name];
  }
  
  // Fallback to a default icon if no mapping exists
  console.warn(`Icon name "${name}" is not valid and has no mapping`);
  return 'help-circle-outline';
};

export default getValidIconName; 