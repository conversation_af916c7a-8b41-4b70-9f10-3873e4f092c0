import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { getBills } from './storage'; // Assuming getBills is in storage.ts
import { Bill } from '@/types/bill'; // Assuming Bill type definition

const DAILY_SALES_NOTIFICATION_ID = 'daily-sales-summary';

// Function to calculate today's sales total
export async function getDailySalesSummary(): Promise<{ totalSales: number; billsCount: number }> {
  try {
    const bills = await getBills();
    const today = new Date().toISOString().split('T')[0];
    const todayBills = bills.filter(
      (bill) => new Date(bill.date).toISOString().split('T')[0] === today
    );
    const totalSales = todayBills.reduce((sum, bill) => sum + (bill.finalAmount ?? bill.totalAmount), 0);
    return { totalSales, billsCount: todayBills.length };
  } catch (error) {
    console.error('Error calculating daily sales summary:', error);
    return { totalSales: 0, billsCount: 0 };
  }
}

// Function to request notification permissions
export async function requestNotificationPermissionsAsync(): Promise<boolean> {
  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  const { status: existingStatus } = await Notifications.getPermissionsAsync();
  let finalStatus = existingStatus;

  if (existingStatus !== 'granted') {
    const { status } = await Notifications.requestPermissionsAsync();
    finalStatus = status;
  }

  return finalStatus === 'granted';
}

// Function to schedule the daily sales notification
export async function scheduleDailySalesNotificationAsync(): Promise<void> {
  const hasPermission = await requestNotificationPermissionsAsync();
  if (!hasPermission) {
    console.log('Notification permission not granted. Cannot schedule daily sales summary.');
    return;
  }

  await Notifications.cancelScheduledNotificationAsync(DAILY_SALES_NOTIFICATION_ID);

  const { totalSales, billsCount } = await getDailySalesSummary();
  
  const formattedSales = new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    maximumFractionDigits: 0,
  }).format(totalSales);

  const notificationContent: Notifications.NotificationContentInput = {
    title: 'Daily Sales Summary 🎉',
    body: `Today's Total Sales: ${formattedSales} from ${billsCount} bill(s). Great job!`,
    data: { type: 'daily-summary' },
  };

  try {
    await Notifications.scheduleNotificationAsync({
      content: notificationContent,
      trigger: {
        hour: 21, // 9 PM
        minute: 0,
        repeats: true,
        channelId: 'default', // Required for Android, ensure channel is set
      },
      identifier: DAILY_SALES_NOTIFICATION_ID,
    });
    console.log('Daily sales summary notification scheduled successfully for 9 PM daily.');
  } catch (error) {
    console.error('Error scheduling daily sales notification:', error);
  }
}

// Function to cancel the daily sales notification
export async function cancelDailySalesNotificationAsync(): Promise<void> {
  try {
    await Notifications.cancelScheduledNotificationAsync(DAILY_SALES_NOTIFICATION_ID);
    console.log('Daily sales summary notification cancelled.');
  } catch (error) {
    console.error('Error cancelling daily sales notification:', error);
  }
}

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: true, // Added for iOS
    shouldShowList: true,   // Added for iOS
  }),
}); 