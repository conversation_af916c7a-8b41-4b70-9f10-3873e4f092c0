/**
 * Defines the preset date filter options available throughout the app
 */
export type DateFilterPreset = 
  | 'all' 
  | 'today' 
  | 'yesterday' 
  | 'this_week' 
  | 'last_week' 
  | 'this_month' 
  | 'last_month' 
  | 'custom';

/**
 * Get date range based on preset filter.
 * 
 * @param preset The preset filter to use
 * @param customRange For 'custom' preset only - the custom start/end dates
 * @returns Object with start and end dates
 */
export const getDateRangeFromPreset = (
  preset: DateFilterPreset,
  customRange?: { startDate?: Date, endDate?: Date }
): { start?: Date, end?: Date } => {
  const today = new Date();
  today.setHours(23, 59, 59, 999); // End of today
  
  const startOfToday = new Date(today);
  startOfToday.setHours(0, 0, 0, 0); // Start of today
  
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  yesterday.setHours(0, 0, 0, 0); // Start of yesterday
  
  const endOfYesterday = new Date(today);
  endOfYesterday.setDate(endOfYesterday.getDate() - 1);
  endOfYesterday.setHours(23, 59, 59, 999); // End of yesterday
  
  const startOfThisWeek = new Date(today);
  startOfThisWeek.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1)); // Set to Monday
  startOfThisWeek.setHours(0, 0, 0, 0);
  
  const endOfLastWeek = new Date(startOfThisWeek);
  endOfLastWeek.setDate(endOfLastWeek.getDate() - 1);
  endOfLastWeek.setHours(23, 59, 59, 999);
  
  const startOfLastWeek = new Date(startOfThisWeek);
  startOfLastWeek.setDate(startOfLastWeek.getDate() - 7);
  startOfLastWeek.setHours(0, 0, 0, 0);
  
  const startOfThisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  startOfThisMonth.setHours(0, 0, 0, 0);
  
  const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
  startOfLastMonth.setHours(0, 0, 0, 0);
  
  const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
  endOfLastMonth.setHours(23, 59, 59, 999);
  
  switch (preset) {
    case 'today':
      return { start: startOfToday, end: today };
    case 'yesterday':
      return { start: yesterday, end: endOfYesterday };
    case 'this_week':
      return { start: startOfThisWeek, end: today };
    case 'last_week':
      return { start: startOfLastWeek, end: endOfLastWeek };
    case 'this_month':
      return { start: startOfThisMonth, end: today };
    case 'last_month':
      return { start: startOfLastMonth, end: endOfLastMonth };
    case 'custom':
      if (!customRange) return { start: undefined, end: undefined };
      
      return { 
        start: customRange.startDate, 
        end: customRange.endDate ? 
          (() => {
            // Set end date to end of day
            const endDate = new Date(customRange.endDate);
            endDate.setHours(23, 59, 59, 999);
            return endDate;
          })() : undefined
      };
    case 'all':
    default:
      return { start: undefined, end: undefined };
  }
};

/**
 * Formats a date range to a human-readable string.
 * 
 * @param startDate Start date of the range
 * @param endDate End date of the range
 * @returns Formatted string
 */
export const formatDateRange = (startDate?: Date, endDate?: Date): string => {
  if (!startDate) return 'All Time';
  
  if (!endDate) return `Since ${startDate.toLocaleDateString()}`;
  
  return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
};

/**
 * Helper to set a date to the start of day (00:00:00)
 */
export const setToStartOfDay = (date: Date): Date => {
  const newDate = new Date(date);
  newDate.setHours(0, 0, 0, 0);
  return newDate;
};

/**
 * Helper to set a date to the end of day (23:59:59.999)
 */
export const setToEndOfDay = (date: Date): Date => {
  const newDate = new Date(date);
  newDate.setHours(23, 59, 59, 999);
  return newDate;
}; 