import { database, billsCollection, customersCollection, itemsCollection, paymentsCollection } from '../database'
import { Q } from '@nozbe/watermelondb'
import { sectionBy, mapObj } from '@nozbe/watermelondb/utils/fp'

// Cached query factories - allows WatermelonDB to reuse compiled queries
const billQueries = {
  byStatus: status => billsCollection.query(Q.where('status', status)),
  byCustomer: customerId => billsCollection.query(Q.where('customer_id', customerId)),
  recentBills: days => {
    const cutoffDate = Date.now() - (days * 24 * 60 * 60 * 1000)
    return billsCollection.query(
      Q.where('date', Q.gte(cutoffDate)),
      Q.sortBy('date', 'desc')
    )
  },
  byDateRange: (startDate, endDate) => billsCollection.query(
    Q.and(
      Q.where('date', Q.gte(startDate)),
      Q.where('date', Q.lte(endDate))
    ),
    Q.sortBy('date', 'desc')
  )
}

const customerQueries = {
  byPhone: phone => customersCollection.query(Q.where('phone', phone)),
  alphabetical: () => customersCollection.query(Q.sortBy('name', 'asc'))
}

// Customer operations
export const CustomerService = {
  async getAll() {
    return await customerQueries.alphabetical().fetch()
  },
  
  async getById(id) {
    return await customersCollection.find(id)
  },
  
  async findByPhone(phone) {
    return await customerQueries.byPhone(phone).fetch()
  },
  
  async bulkCreate(customers) {
    return await database.write(async () => {
      return await Promise.all(
        customers.map(data => 
          customersCollection.create(customer => {
            customer.name = data.name
            customer.phone = data.phone
            customer.email = data.email || null
            customer.address = data.address || null
          })
        )
      )
    })
  },
  
  async create(data) {
    return await database.write(async () => {
      return await customersCollection.create(customer => {
        customer.name = data.name
        customer.phone = data.phone
        customer.email = data.email || null
        customer.address = data.address || null
      })
    })
  },
  
  async update(id, data) {
    return await database.write(async () => {
      const customer = await customersCollection.find(id)
      return await customer.update(customer => {
        customer.name = data.name
        customer.phone = data.phone
        customer.email = data.email || customer.email
        customer.address = data.address || customer.address
      })
    })
  },
  
  async delete(id) {
    return await database.write(async () => {
      const customer = await customersCollection.find(id)
      return await customer.markAsDeleted()
    })
  },
  
  // Get customers grouped by first letter (for alphabetical listing)
  async getGroupedByAlphabet() {
    const customers = await this.getAll()
    return sectionBy(
      customer => customer.name.charAt(0).toUpperCase(),
      customers
    )
  }
}

// Bill operations
export const BillService = {
  async getAll() {
    return await billsCollection.query(Q.sortBy('date', 'desc')).fetch()
  },
  
  async getAllWithCustomers() {
    return await billsCollection.query(
      Q.experimentalJoinTables(['customers']),
      Q.sortBy('date', 'desc')
    ).fetch()
  },
  
  async getById(id) {
    return await billsCollection.find(id)
  },
  
  async getByBillNumber(billNumber) {
    return await billsCollection.query(
      Q.where('bill_number', billNumber)
    ).fetch()
  },
  
  async getByCustomerId(customerId) {
    return await billQueries.byCustomer(customerId).fetch()
  },
  
  async getByStatus(status) {
    return await billQueries.byStatus(status).fetch()
  },
  
  async getRecentBills(days = 7) {
    return await billQueries.recentBills(days).fetch()
  },
  
  async getByDateRange(startDate, endDate) {
    return await billQueries.byDateRange(startDate, endDate).fetch()
  },
  
  async bulkCreate(bills) {
    return await database.write(async () => {
      const createdBills = []
      
      for (const billData of bills) {
        const bill = await billsCollection.create(bill => {
          bill.billNumber = billData.billNumber
          bill.date = billData.date || new Date()
          bill.customerId = billData.customerId
          bill.totalAmount = billData.totalAmount || 0
          bill.discount = billData.discount || 0
          bill.tax = billData.tax || 0
          bill.status = billData.status || 'pending'
          bill.notes = billData.notes
        })
        
        if (billData.items && billData.items.length > 0) {
          await Promise.all(billData.items.map(async (item) => {
            return await itemsCollection.create(newItem => {
              newItem.billId = bill.id
              newItem.name = item.name
              newItem.quantity = item.quantity
              newItem.price = item.price
              newItem.total = item.quantity * item.price
            })
          }))
        }
        
        createdBills.push(bill)
      }
      
      return createdBills
    })
  },
  
  async create(data) {
    return await database.write(async () => {
      const bill = await billsCollection.create(bill => {
        bill.billNumber = data.billNumber
        bill.date = data.date || new Date()
        bill.customerId = data.customerId
        bill.totalAmount = data.totalAmount || 0
        bill.discount = data.discount || 0
        bill.tax = data.tax || 0
        bill.status = data.status || 'pending'
        bill.notes = data.notes
      })
      
      // Create bill items if provided
      if (data.items && data.items.length > 0) {
        await Promise.all(data.items.map(async (item) => {
          return await itemsCollection.create(newItem => {
            newItem.billId = bill.id
            newItem.name = item.name
            newItem.quantity = item.quantity
            newItem.price = item.price
            newItem.total = item.quantity * item.price
          })
        }))
      }
      
      return bill
    })
  },
  
  async update(id, data) {
    return await database.write(async () => {
      const bill = await billsCollection.find(id)
      return await bill.update(bill => {
        if (data.billNumber) bill.billNumber = data.billNumber
        if (data.date) bill.date = data.date
        if (data.customerId) bill.customerId = data.customerId
        if (data.totalAmount !== undefined) bill.totalAmount = data.totalAmount
        if (data.discount !== undefined) bill.discount = data.discount
        if (data.tax !== undefined) bill.tax = data.tax
        if (data.status) bill.status = data.status
        if (data.notes !== undefined) bill.notes = data.notes
      })
    })
  },
  
  async delete(id) {
    return await database.write(async () => {
      // First delete related items and payments in a single transaction
      const [items, payments] = await Promise.all([
        itemsCollection.query(Q.where('bill_id', id)).fetch(),
        paymentsCollection.query(Q.where('bill_id', id)).fetch()
      ])
      
      // Delete all items
      for (const item of items) {
        await item.markAsDeleted()
      }
      
      // Delete all payments
      for (const payment of payments) {
        await payment.markAsDeleted()
      }
      
      // Delete the bill
      const bill = await billsCollection.find(id)
      return await bill.markAsDeleted()
    })
  },
  
  // Get stats by status for dashboard
  async getStatusStats() {
    const [pending, paid, all] = await Promise.all([
      billQueries.byStatus('pending').fetch(),
      billQueries.byStatus('paid').fetch(),
      this.getAll()
    ])
    
    return {
      total: all.length,
      pending: {
        count: pending.length,
        amount: pending.reduce((sum, bill) => sum + bill.totalAmount, 0)
      },
      paid: {
        count: paid.length,
        amount: paid.reduce((sum, bill) => sum + bill.totalAmount, 0)
      }
    }
  }
}

// Item operations
export const ItemService = {
  async getByBillId(billId) {
    return await itemsCollection.query(
      Q.where('bill_id', billId)
    ).fetch()
  },
  
  async bulkCreate(items) {
    return await database.write(async () => {
      return await Promise.all(
        items.map(data => 
          itemsCollection.create(item => {
            item.billId = data.billId
            item.name = data.name
            item.quantity = data.quantity
            item.price = data.price
            item.total = data.quantity * data.price
          })
        )
      )
    })
  },
  
  async create(data) {
    return await database.write(async () => {
      return await itemsCollection.create(item => {
        item.billId = data.billId
        item.name = data.name
        item.quantity = data.quantity
        item.price = data.price
        item.total = data.quantity * data.price
      })
    })
  },
  
  async update(id, data) {
    return await database.write(async () => {
      const item = await itemsCollection.find(id)
      return await item.update(item => {
        if (data.name) item.name = data.name
        if (data.quantity !== undefined) item.quantity = data.quantity
        if (data.price !== undefined) item.price = data.price
        item.total = item.quantity * item.price
      })
    })
  },
  
  async delete(id) {
    return await database.write(async () => {
      const item = await itemsCollection.find(id)
      return await item.markAsDeleted()
    })
  }
}

// Payment operations
export const PaymentService = {
  async getByBillId(billId) {
    return await paymentsCollection.query(
      Q.where('bill_id', billId),
      Q.sortBy('date', 'desc')
    ).fetch()
  },
  
  async bulkCreate(payments) {
    return await database.write(async () => {
      return await Promise.all(
        payments.map(data => 
          paymentsCollection.create(payment => {
            payment.billId = data.billId
            payment.amount = data.amount
            payment.method = data.method
            payment.date = data.date || new Date()
            payment.reference = data.reference || null
          })
        )
      )
    })
  },
  
  async create(data) {
    return await database.write(async () => {
      return await paymentsCollection.create(payment => {
        payment.billId = data.billId
        payment.amount = data.amount
        payment.method = data.method
        payment.date = data.date || new Date()
        payment.reference = data.reference || null
      })
    })
  },
  
  async update(id, data) {
    return await database.write(async () => {
      const payment = await paymentsCollection.find(id)
      return await payment.update(payment => {
        if (data.amount !== undefined) payment.amount = data.amount
        if (data.method) payment.method = data.method
        if (data.date) payment.date = data.date
        if (data.reference !== undefined) payment.reference = data.reference
      })
    })
  },
  
  async delete(id) {
    return await database.write(async () => {
      const payment = await paymentsCollection.find(id)
      return await payment.markAsDeleted()
    })
  },
  
  // Get payment stats for reports
  async getPaymentMethodStats(startDate, endDate) {
    const payments = await paymentsCollection.query(
      Q.and(
        Q.where('date', Q.gte(startDate)),
        Q.where('date', Q.lte(endDate))
      )
    ).fetch()
    
    // Group payments by method
    const methodGroups = payments.reduce((acc, payment) => {
      const method = payment.method
      if (!acc[method]) {
        acc[method] = {
          count: 0,
          amount: 0
        }
      }
      
      acc[method].count += 1
      acc[method].amount += payment.amount
      return acc
    }, {})
    
    return methodGroups
  }
} 