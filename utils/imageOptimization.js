import * as FileSystem from 'expo-file-system';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import { Platform } from 'react-native';

// Optimal sizes for different image types
const IMAGE_SIZES = {
  thumbnail: { width: 100, height: 100 },
  profile: { width: 200, height: 200 },
  medium: { width: 400, height: 400 },
  large: { width: 800, height: 800 }
};

// Default JPEG quality
const DEFAULT_QUALITY = 0.8;

// Get the app's document directory for storing optimized images
const getCacheDirectory = async () => {
  const cacheDir = `${FileSystem.cacheDirectory}optimized-images/`;
  
  // Check if directory exists, create if not
  const dirInfo = await FileSystem.getInfoAsync(cacheDir);
  if (!dirInfo.exists) {
    await FileSystem.makeDirectoryAsync(cacheDir, { intermediates: true });
  }
  
  return cacheDir;
};

/**
 * Optimizes an image and saves it to the cache
 * 
 * @param {string} uri - The original image URI
 * @param {string} size - Size preset ('thumbnail', 'profile', 'medium', or 'large')
 * @param {number} quality - JPEG quality (0.0 to 1.0)
 * @returns {Promise<string>} - The URI of the optimized image
 */
export const optimizeImage = async (uri, size = 'medium', quality = DEFAULT_QUALITY) => {
  try {
    if (!uri) return null;
    
    const cacheDir = await getCacheDirectory();
    
    // Generate a filename based on the original URI and settings
    const filename = `${uri.split('/').pop().split('.')[0]}_${size}_${quality}.jpg`;
    const outputUri = `${cacheDir}${filename}`;
    
    // Check if this image is already optimized
    const fileInfo = await FileSystem.getInfoAsync(outputUri);
    if (fileInfo.exists) {
      return outputUri;
    }
    
    // Get the target dimensions
    const dimensions = IMAGE_SIZES[size] || IMAGE_SIZES.medium;
    
    // Process the image
    const result = await manipulateAsync(
      uri,
      [{ resize: dimensions }],
      { 
        compress: quality, 
        format: SaveFormat.JPEG 
      }
    );
    
    // Save to cache directory
    await FileSystem.copyAsync({
      from: result.uri,
      to: outputUri
    });
    
    return outputUri;
  } catch (error) {
    console.error('Image optimization failed:', error);
    return uri; // Return original if optimization fails
  }
};

/**
 * Clears the image optimization cache
 */
export const clearImageCache = async () => {
  try {
    const cacheDir = await getCacheDirectory();
    await FileSystem.deleteAsync(cacheDir, { idempotent: true });
    await FileSystem.makeDirectoryAsync(cacheDir, { intermediates: true });
    console.log('Image cache cleared');
  } catch (error) {
    console.error('Failed to clear image cache:', error);
  }
};

/**
 * Gets information about the current image cache size
 * 
 * @returns {Promise<{count: number, size: number}>} - Cache stats
 */
export const getImageCacheInfo = async () => {
  try {
    const cacheDir = await getCacheDirectory();
    const dirContents = await FileSystem.readDirectoryAsync(cacheDir);
    
    let totalSize = 0;
    
    // Calculate total size
    await Promise.all(dirContents.map(async (file) => {
      const fileInfo = await FileSystem.getInfoAsync(`${cacheDir}${file}`);
      if (fileInfo.exists && !fileInfo.isDirectory) {
        totalSize += fileInfo.size;
      }
    }));
    
    return {
      count: dirContents.length,
      size: totalSize,
      formattedSize: formatFileSize(totalSize)
    };
  } catch (error) {
    console.error('Failed to get cache info:', error);
    return { count: 0, size: 0, formattedSize: '0 B' };
  }
};

/**
 * Helper to format file size in human readable format
 */
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}; 