{"expo": {"name": "Gift Corner", "slug": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "<PERSON><PERSON><PERSON>", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.sparktale.billingapp", "icon": "./assets/images/icon.png"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#ffffff"}, "package": "com.sparktale.billingapp", "icon": "./assets/images/icon.png"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/icon.png"}, "plugins": ["expo-router"], "experiments": {"typedRoutes": true}, "updates": {"enabled": true, "fallbackToCacheTimeout": 15000, "checkAutomatically": "ON_LOAD"}, "runtimeVersion": {"policy": "appVersion"}, "extra": {"router": {"origin": false}, "eas": {"projectId": "8d045526-65aa-4886-bf9a-cf5ac568f216"}}, "jsEngine": "hermes", "owner": "studydjexo"}}