# WatermelonDB Integration

This directory contains the WatermelonDB setup for the Billing App. WatermelonDB is a high-performance, reactive database system that's optimized for React Native.

## Directory Structure

```
database/
├── index.js                # Main database setup and exports
├── migrations.js           # Database migrations
├── models/                 # Database models
│   ├── Bill.js             # Bill model
│   ├── Customer.js         # Customer model
│   ├── Item.js             # Item model
│   ├── Payment.js          # Payment model
│   └── index.js            # Models barrel file
└── schemas/                # Database schemas
    └── index.js            # Schema definitions
```

## Models

The database has the following models:

1. **Bill** - Represents a bill/invoice
2. **Customer** - Represents a customer
3. **Item** - Represents an item in a bill
4. **Payment** - Represents a payment for a bill

## How to Use

### 1. Access the Database

You can access the database through the DatabaseProvider context:

```jsx
import { useDatabase } from '@/components/DatabaseProvider';

function MyComponent() {
  const { database } = useDatabase();
  
  // Now you can use the database
}
```

### 2. Query Collections

To query a collection:

```jsx
const billsCollection = database.get('bills');
const bills = await billsCollection.query().fetch();
```

### 3. Filter and Sort

```jsx
// Filter bills by status
const paidBills = await billsCollection.query(
  Q.where('status', 'paid')
).fetch();

// Sort by date
const sortedBills = await billsCollection.query(
  Q.sortBy('date', 'desc')
).fetch();

// Combine filters
const recentPaidBills = await billsCollection.query(
  Q.where('status', 'paid'),
  Q.where('date', Q.gte(oneWeekAgo)),
  Q.sortBy('date', 'desc')
).fetch();
```

### 4. Create Records

```jsx
await database.write(async () => {
  await customersCollection.create(customer => {
    customer.name = 'John Doe';
    customer.phone = '**********';
    customer.email = '<EMAIL>';
  });
});
```

### 5. Update Records

```jsx
await database.write(async () => {
  const customer = await customersCollection.find(customerId);
  await customer.update(customer => {
    customer.name = 'Jane Doe';
    customer.phone = '9876543210';
  });
});
```

### 6. Delete Records

```jsx
await database.write(async () => {
  const customer = await customersCollection.find(customerId);
  await customer.markAsDeleted();
});
```

### 7. Relationships

```jsx
// Get all items for a bill
const bill = await billsCollection.find(billId);
const items = await bill.items.fetch();

// Get customer for a bill
const customer = await bill.customer.fetch();
```

### 8. Observe Changes (Reactive Queries)

```jsx
// In a React component
useEffect(() => {
  const subscription = billsCollection.query().observe().subscribe(bills => {
    setBills(bills);
  });
  
  return () => subscription.unsubscribe();
}, []);
```

## Services

For convenience, we've created service files with common operations:

- `CustomerService` - Operations for customers
- `BillService` - Operations for bills
- `ItemService` - Operations for items
- `PaymentService` - Operations for payments

Example usage:

```jsx
import { CustomerService } from '@/utils/databaseService';

// Get all customers
const customers = await CustomerService.getAll();

// Create a new customer
const newCustomer = await CustomerService.create({
  name: 'John Doe',
  phone: '**********',
  email: '<EMAIL>'
});
```

## Hooks

We've provided several hooks to make working with WatermelonDB easier:

- `useDatabase()` - Access the database instance
- `useCollection(collection)` - Subscribe to a collection
- `useRecord(collection, id)` - Subscribe to a single record

Example usage:

```jsx
import { useCollection } from '@/hooks/useDatabase';
import { billsCollection } from '@/database';

function BillsList() {
  const { data: bills, isLoading } = useCollection(billsCollection);
  
  if (isLoading) return <LoadingIndicator />;
  
  return (
    <FlatList
      data={bills}
      renderItem={({ item }) => <BillItem bill={item} />}
    />
  );
}
```

## JSI Adapter and Performance

WatermelonDB uses a JavaScript Interface (JSI) for direct communication with SQLite, which provides significant performance benefits. However, there are some important notes about this feature:

### Warning about JSI Adapter

When you see this warning:
```
WARN [🍉] JSI SQLiteAdapter not available… falling back to asynchronous operation. This will happen if you're using remote debugger, and may happen if you forgot to recompile native app after WatermelonDB update
```

This is normal during development when using the remote debugger. The JSI adapter cannot work with remote debugging enabled.

### How We Handle This

Our custom adapter in `database/customAdapter.js` automatically detects if remote debugging is enabled and configures WatermelonDB appropriately:

1. During development with remote debugging: Uses asynchronous mode (slower but compatible)
2. In production or without remote debugging: Uses JSI mode (much faster)

### For Maximum Performance

To get the full performance benefits of WatermelonDB:

1. Disable remote debugging when testing performance
2. Make sure to build a production release when deploying your app
3. The production build will automatically use JSI mode for best performance

Remember that the database will work correctly in both modes, but JSI mode is significantly faster. 