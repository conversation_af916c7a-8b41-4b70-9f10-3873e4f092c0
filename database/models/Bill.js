import { Model } from '@nozbe/watermelondb'
import { field, date, children, relation, readonly, lazy } from '@nozbe/watermelondb/decorators'

export default class Bill extends Model {
  static table = 'bills'
  
  static associations = {
    customers: { type: 'belongs_to', key: 'customer_id' },
    items: { type: 'has_many', foreignKey: 'bill_id' },
    payments: { type: 'has_many', foreignKey: 'bill_id' }
  }

  // Frequently accessed fields - eager loaded
  @field('bill_number') billNumber
  @field('customer_id') customerId
  @field('total_amount') totalAmount
  @field('status') status
  @date('date') date
  
  // Less frequently accessed fields - lazy loaded
  @lazy @field('discount') discount
  @lazy @field('tax') tax
  @lazy @field('notes') notes
  
  // Read-only fields
  @readonly @date('created_at') createdAt
  @readonly @date('updated_at') updatedAt
  
  // Relations
  @relation('customers', 'customer_id') customer
  @children('items') items
  @children('payments') payments
  
  // Computed properties
  get remainingBalance() {
    return this._computeRemainingBalance()
  }
  
  // Private methods
  async _computeRemainingBalance() {
    try {
      const payments = await this.payments.fetch()
      const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0)
      return this.totalAmount - totalPaid
    } catch (error) {
      console.error('Error calculating remaining balance:', error)
      return this.totalAmount
    }
  }
  
  // Helper method to get all items with a single query
  async getItemsWithDetails() {
    return await this.items.fetch()
  }
  
  // Helper method to get customer details with a single query
  async getCustomerDetails() {
    return await this.customer.fetch()
  }
} 