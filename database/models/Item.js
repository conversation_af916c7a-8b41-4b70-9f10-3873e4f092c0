import { Model } from '@nozbe/watermelondb'
import { field, date, relation, readonly } from '@nozbe/watermelondb/decorators'

export default class Item extends Model {
  static table = 'items'
  
  static associations = {
    bills: { type: 'belongs_to', key: 'bill_id' }
  }

  @field('bill_id') billId
  @field('name') name
  @field('quantity') quantity
  @field('price') price
  @field('total') total
  @readonly @date('created_at') createdAt
  @readonly @date('updated_at') updatedAt
  
  @relation('bills', 'bill_id') bill
  
  // Calculate total before saving
  async beforeSave() {
    this.total = this.quantity * this.price
  }
} 