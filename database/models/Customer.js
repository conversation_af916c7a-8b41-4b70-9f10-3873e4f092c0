import { Model } from '@nozbe/watermelondb'
import { field, date, children, readonly } from '@nozbe/watermelondb/decorators'

export default class Customer extends Model {
  static table = 'customers'
  
  static associations = {
    bills: { type: 'has_many', foreignKey: 'customer_id' }
  }

  @field('name') name
  @field('phone') phone
  @field('email') email
  @field('address') address
  @readonly @date('created_at') createdAt
  @readonly @date('updated_at') updatedAt
  
  @children('bills') bills
  
  // Helper method to get all bills
  async getBills() {
    return await this.bills.fetch()
  }
  
  // Helper method to get total amount spent
  async getTotalSpent() {
    const bills = await this.getBills()
    return bills.reduce((sum, bill) => sum + bill.totalAmount, 0)
  }
} 