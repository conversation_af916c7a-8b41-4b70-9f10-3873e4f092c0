import { Model } from '@nozbe/watermelondb'
import { field, date, relation, readonly } from '@nozbe/watermelondb/decorators'

export default class Payment extends Model {
  static table = 'payments'
  
  static associations = {
    bills: { type: 'belongs_to', key: 'bill_id' }
  }

  @field('bill_id') billId
  @field('amount') amount
  @field('method') method
  @field('reference') reference
  @date('date') date
  @readonly @date('created_at') createdAt
  @readonly @date('updated_at') updatedAt
  
  @relation('bills', 'bill_id') bill
} 