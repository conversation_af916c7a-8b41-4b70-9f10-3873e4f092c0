const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Define paths
const rootDir = process.cwd();
const androidResDir = path.join(rootDir, 'android', 'app', 'src', 'main', 'res');
const tempDir = path.join(rootDir, 'temp_icons');

// Check if we're in the right directory
if (!fs.existsSync(path.join(rootDir, 'package.json'))) {
  console.error('Error: Run this script from the root of your React Native project');
  process.exit(1);
}

// Ensure the icon files exist
const requiredIcons = ['adaptive-icon.png', 'icon.png', 'favicon.png'];
for (const icon of requiredIcons) {
  if (!fs.existsSync(path.join(rootDir, icon))) {
    console.error(`Error: ${icon} not found in the project root directory`);
    process.exit(1);
  }
}

console.log('Processing icons for Android...');

// Define densities and sizes
const densities = ['mdpi', 'hdpi', 'xhdpi', 'xxhdpi', 'xxxhdpi'];
const launcherSizes = [48, 72, 96, 144, 192];
const foregroundSizes = [108, 162, 216, 324, 432];

// Create necessary directories
densities.forEach(density => {
  const dir = path.join(androidResDir, `mipmap-${density}`);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Ensure anydpi-v26 directory exists
const anydpiDir = path.join(androidResDir, 'mipmap-anydpi-v26');
if (!fs.existsSync(anydpiDir)) {
  fs.mkdirSync(anydpiDir, { recursive: true });
}

// Copy icon files directly (since we can't resize without additional libraries)
console.log('Copying icon files to appropriate directories...');

// Copy adaptive icon
densities.forEach((density, i) => {
  const targetDir = path.join(androidResDir, `mipmap-${density}`);
  
  // Copy the regular icon
  fs.copyFileSync(
    path.join(rootDir, 'icon.png'),
    path.join(targetDir, 'ic_launcher.png')
  );
  
  // Copy the round icon (using the same source)
  fs.copyFileSync(
    path.join(rootDir, 'icon.png'),
    path.join(targetDir, 'ic_launcher_round.png')
  );
  
  // Copy the adaptive icon foreground
  fs.copyFileSync(
    path.join(rootDir, 'adaptive-icon.png'),
    path.join(targetDir, 'ic_launcher_foreground.png')
  );
});

// Update the adaptive icon XML files
console.log('Updating adaptive icon configuration...');

// ic_launcher.xml
fs.writeFileSync(
  path.join(anydpiDir, 'ic_launcher.xml'),
  `<?xml version="1.0" encoding="utf-8"?>
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
    <background android:drawable="@color/iconBackground"/>
    <foreground android:drawable="@mipmap/ic_launcher_foreground"/>
</adaptive-icon>
`
);

// ic_launcher_round.xml
fs.writeFileSync(
  path.join(anydpiDir, 'ic_launcher_round.xml'),
  `<?xml version="1.0" encoding="utf-8"?>
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
    <background android:drawable="@color/iconBackground"/>
    <foreground android:drawable="@mipmap/ic_launcher_foreground"/>
</adaptive-icon>
`
);

// Update the colors.xml file to match the icon's theme
console.log('Updating icon background color...');
const colorsPath = path.join(androidResDir, 'values', 'colors.xml');
let colorsContent = fs.readFileSync(colorsPath, 'utf8');

// Update the iconBackground color - using a teal color that matches the Gift Corner theme
colorsContent = colorsContent.replace(
  /<color name="iconBackground">#[a-fA-F0-9]{6}<\/color>/,
  '<color name="iconBackground">#42A5B2</color>'
);

fs.writeFileSync(colorsPath, colorsContent);

console.log('Icon update complete!');
console.log('NOTE: For proper resizing of icons, you should use a tool like sharp or ImageMagick.');
console.log('Please rebuild your app to see the changes.'); 