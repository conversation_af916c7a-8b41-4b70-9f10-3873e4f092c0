#!/bin/bash

# Script to update Android app icons
echo "Updating app icons for Android..."

# Get the base directory
BASE_DIR="$(pwd)"
ANDROID_RES_DIR="${BASE_DIR}/android/app/src/main/res"

# Create directory for icon processing if it doesn't exist
mkdir -p "${BASE_DIR}/temp_icons/processed"

# Check if the icons exist
if [ ! -f "adaptive-icon.png" ] || [ ! -f "icon.png" ] || [ ! -f "favicon.png" ]; then
  echo "Error: Required icon files not found. Please make sure adaptive-icon.png, icon.png, and favicon.png are in the current directory."
  exit 1
fi

# Copy and prepare the icons
echo "Processing icons..."

# For adaptive icon (foreground)
cp "adaptive-icon.png" "${BASE_DIR}/temp_icons/processed/ic_launcher_foreground.png"
# For round and regular icons
cp "icon.png" "${BASE_DIR}/temp_icons/processed/ic_launcher.png"
cp "icon.png" "${BASE_DIR}/temp_icons/processed/ic_launcher_round.png"

# Update the icon background color in colors.xml
# Extract dominant color from the adaptive icon or use default
echo "Updating icon background color..."
sed -i 's/<color name="iconBackground">#ffffff<\/color>/<color name="iconBackground">#42A5B2<\/color>/' "${ANDROID_RES_DIR}/values/colors.xml"

# Update icons for different densities
echo "Copying icons to appropriate mipmap directories..."

# Define densities and sizes
densities=("mdpi" "hdpi" "xhdpi" "xxhdpi" "xxxhdpi")
launcher_sizes=(48 72 96 144 192)
foreground_sizes=(108 162 216 324 432)

# Update icons for each density
for i in "${!densities[@]}"; do
  density="${densities[$i]}"
  launcher_size="${launcher_sizes[$i]}"
  foreground_size="${foreground_sizes[$i]}"
  
  # Create directory if it doesn't exist
  mkdir -p "${ANDROID_RES_DIR}/mipmap-${density}"
  
  # Copy and resize launcher icons
  echo "Updating ${density} icons..."
  cp "${BASE_DIR}/temp_icons/processed/ic_launcher.png" "${ANDROID_RES_DIR}/mipmap-${density}/ic_launcher.png"
  cp "${BASE_DIR}/temp_icons/processed/ic_launcher_round.png" "${ANDROID_RES_DIR}/mipmap-${density}/ic_launcher_round.png"
  cp "${BASE_DIR}/temp_icons/processed/ic_launcher_foreground.png" "${ANDROID_RES_DIR}/mipmap-${density}/ic_launcher_foreground.png"
done

# Update adaptive icon XML files
echo "Updating adaptive icon configuration..."
cat > "${ANDROID_RES_DIR}/mipmap-anydpi-v26/ic_launcher.xml" << EOF
<?xml version="1.0" encoding="utf-8"?>
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
    <background android:drawable="@color/iconBackground"/>
    <foreground android:drawable="@mipmap/ic_launcher_foreground"/>
</adaptive-icon>
EOF

cat > "${ANDROID_RES_DIR}/mipmap-anydpi-v26/ic_launcher_round.xml" << EOF
<?xml version="1.0" encoding="utf-8"?>
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
    <background android:drawable="@color/iconBackground"/>
    <foreground android:drawable="@mipmap/ic_launcher_foreground"/>
</adaptive-icon>
EOF

echo "Icon update complete! Please rebuild your app to see the changes." 