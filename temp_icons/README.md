# Gift Corner App Icon Update

This directory contains scripts to update the app icons for your Gift Corner app.

## Prerequisites

Make sure you have the following icon files in your project root directory:
- `adaptive-icon.png` - The foreground of the adaptive icon (transparent background)
- `icon.png` - The main app icon
- `favicon.png` - Favicon for web

## How to Update Icons

1. Place the icon files (`adaptive-icon.png`, `icon.png`, and `favicon.png`) in the project root directory.

2. Run the icon processing script:
   ```
   node temp_icons/process_icons.js
   ```

3. Rebuild your app:
   ```
   npm run android
   ```

## Icon Specifications

For best results, your icons should follow these specifications:

- **icon.png**: 1024x1024 px, PNG format
- **adaptive-icon.png**: 1024x1024 px, PNG format with transparent background
- **favicon.png**: 196x196 px, PNG format

## Notes

- This script directly copies your icons to the appropriate directories without resizing them.
- For proper resizing, you would need to use an image processing library like Sharp or ImageMagick.
- The background color for the adaptive icon is set to a teal color (#42A5B2) to match the Gift Corner theme.

## Troubleshooting

If you encounter any issues:
1. Make sure all required icon files are in the project root
2. Check that the script has proper permissions
3. Make sure you're running the script from the project root directory 