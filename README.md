# GIFT CORNER Billing App

A simple billing application for GIFT CORNER shop built with Expo and React Native.

## Features

- Create new bills with price and quantity inputs
- Print bills or share them as PDFs
- Save bills with custom names for future reference
- View bill history and search through past bills
- Display current date and time on bills
- Indian Rupee (₹) currency format
- Clean and optimized user interface
- Printer-friendly bill format

## Setup and Running

1. Install dependencies:
   ```
   npm install
   ```

2. Start the app:
   ```
   npm start
   ```

3. Scan the QR code with Expo Go app or run on a simulator/emulator.

## Navigation

- Home: Overview of today's sales and recent bills
- New Bill: Create and print new bills
- History: View and manage saved bills

## Technologies Used

- Expo/React Native
- React Native Paper for UI components
- AsyncStorage for local data persistence
- Expo Print for generating and printing bills
- Expo Sharing for PDF sharing functionality

# Welcome to your Expo app 👋

This is an [Expo](https://expo.dev) project created with [`create-expo-app`](https://www.npmjs.com/package/create-expo-app).

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
   npx expo start
   ```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## Get a fresh project

When you're ready, run:

```bash
npm run reset-project
```

This command will move the starter code to the **app-example** directory and create a blank **app** directory where you can start developing.

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.
# billing_gc_New
# billing_gc_New
# billing_gc_New

# Billing App - Printer Setup

This application supports multiple methods for connecting to thermal receipt printers:

## Printer Connection Options

### 1. Bluetooth Connection
- Requires Bluetooth and Location permissions on Android
- Scans for nearby Bluetooth printers
- Connects and saves printer information for future use
- Supported by most mobile thermal printers

### 2. Wi-Fi Connection
- Discovers network printers on the local network
- Allows manual IP address and port entry
- No location permission required
- Ideal for fixed-position printers in a store

### 3. USB Connection
- Connect directly via USB cable
- May require specific USB permissions on Android
- Limited support on iOS due to restrictions
- Best for stable, high-speed printing

## How to Set Up a Printer

1. Open the Printer Setup screen
2. Select your preferred connection type (Bluetooth, Wi-Fi, or USB)
3. Tap "Scan for Printers" to discover available printers
4. For Wi-Fi printers, you can also manually enter an IP address
5. Tap on a discovered printer to connect to it
6. Once connected, the printer will be saved for future use

## Troubleshooting

### Bluetooth Connection Issues
- Ensure Bluetooth is enabled on your device
- Enable Location services (required for Bluetooth scanning on Android)
- Make sure the printer is powered on and in pairing mode
- Try restarting the printer and your device

### Wi-Fi Connection Issues
- Verify the printer is connected to the same network as your device
- Check that the correct IP address and port are entered
- Ensure no firewall is blocking the connection
- Try using a static IP for your printer

### USB Connection Issues
- Make sure you have the correct USB cable
- Some devices may require OTG (On-The-Go) adapters
- Allow USB permissions when prompted
- Try different USB ports if available

## Supported Printer Models

This application is designed to work with ESC/POS compatible thermal receipt printers, including but not limited to:

- Epson TM series
- Star Micronics TSP series
- Bixolon SRP series
- And many other generic thermal receipt printers

For best results, use printers that support standard ESC/POS commands.
