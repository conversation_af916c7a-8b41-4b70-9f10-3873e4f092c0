import React, { createContext, useContext, useEffect, useState } from 'react'
import { View, Text, ActivityIndicator } from 'react-native'
import { database } from '../database'

// Create context
const DatabaseContext = createContext(null)

export const DatabaseProvider = ({ children }) => {
  const [isReady, setIsReady] = useState(false)
  const [error, setError] = useState(null)

  useEffect(() => {
    const initDatabase = async () => {
      try {
        // Perform any initialization if needed
        await database.write(async () => {
          // You can add initial data here if needed
        })
        setIsReady(true)
      } catch (e) {
        console.error('Failed to initialize database:', e)
        setError(e)
      }
    }

    initDatabase()
  }, [])

  if (error) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: 'red' }}>Database Error: {error.message}</Text>
      </View>
    )
  }

  if (!isReady) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={{ marginTop: 10 }}>Initializing database...</Text>
      </View>
    )
  }

  return (
    <DatabaseContext.Provider value={{ database, isReady }}>
      {children}
    </DatabaseContext.Provider>
  )
}

// Custom hook to use the database context
export const useDatabase = () => {
  const context = useContext(DatabaseContext)
  if (context === null) {
    throw new Error('useDatabase must be used within a DatabaseProvider')
  }
  return context
} 