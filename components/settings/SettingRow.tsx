import React, { ReactNode } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, TouchableRipple, useTheme, type MD3Theme as ReactNativePaperTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface SettingRowProps {
  label: string;
  description?: string;
  icon?: any;
  iconColor?: string;
  children?: ReactNode;
  onPress?: () => void;
}

const SettingRow: React.FC<SettingRowProps> = ({
  label,
  description,
  icon,
  iconColor,
  children,
  onPress
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  
  const rowContent = (
    <>
      <View style={styles.settingTextContainer}>
        {icon && (
          <MaterialCommunityIcons 
            name={icon} 
            size={24} 
            color={iconColor || theme.colors.primary} 
            style={styles.settingIcon} 
          />
        )}
        <View style={{ flexShrink: 1 }}>
          <Text style={styles.settingLabel}>{label}</Text>
          {description && (
            <Text style={styles.settingDescription}>
              {description}
            </Text>
          )}
        </View>
      </View>
      {children && (
        <View style={styles.childrenContainer}>
          {children}
        </View>
      )}
      {onPress && !children && (
        <MaterialCommunityIcons name="chevron-right" size={24} color={theme.colors.onSurfaceVariant} />
      )}
    </>
  );
  
  if (onPress) {
    return (
      <TouchableRipple onPress={onPress} style={styles.settingRow}>
        <View style={styles.rowInnerContainer}>
          {rowContent}
        </View>
      </TouchableRipple>
    );
  }
  
  return <View style={styles.settingRow}>{rowContent}</View>;
};

const createStyles = (theme: ReactNativePaperTheme) => StyleSheet.create({
  settingRow: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 1,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    overflow: 'hidden',
  },
  rowInnerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  settingTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexShrink: 1,
  },
  settingIcon: {
    marginRight: 16,
  },
  settingLabel: {
    fontSize: 17,
    color: theme.colors.onSurface,
  },
  settingDescription: {
    fontSize: 13,
    color: theme.colors.onSurfaceVariant,
    marginTop: 2,
    flexShrink: 1,
  },
  childrenContainer: {
    marginLeft: 8,
  }
});

export default SettingRow; 