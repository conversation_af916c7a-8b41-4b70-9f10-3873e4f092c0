import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Switch, Text, useTheme, type MD3Theme as ReactNativePaperTheme } from 'react-native-paper';
import SettingRow from './SettingRow';

interface AppPreferencesSectionProps {
  isDarkMode: boolean;
  onToggleTheme: () => void;
  onPressPrinterSetup: () => void;
  dailySummaryEnabled: boolean;
  onToggleDailySummary: (value: boolean) => void;
}

const AppPreferencesSection: React.FC<AppPreferencesSectionProps> = ({
  isDarkMode,
  onToggleTheme,
  onPressPrinterSetup,
  dailySummaryEnabled,
  onToggleDailySummary
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <>
      <Text style={styles.sectionTitle}>App Preferences</Text>
      
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Display & Appearance</Text>
          
          <SettingRow
            label="Dark Mode"
            description="Enable dark theme for the app"
            icon="theme-light-dark"
          >
            <Switch value={isDarkMode} onValueChange={onToggleTheme} />
          </SettingRow>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Notifications</Text>

          <SettingRow
            label="Daily Sales Summary"
            description="Receive a notification with today's sales total at the end of the day."
            icon="chart-bell-curve-cumulative"
          >
            <Switch value={dailySummaryEnabled} onValueChange={onToggleDailySummary} />
          </SettingRow>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Printer Configuration</Text>

          <SettingRow
            label="Printer Setup"
            description="Configure your Bluetooth thermal printer"
            icon="printer"
            onPress={onPressPrinterSetup}
          />
        </Card.Content>
      </Card>
    </>
  );
};

const createStyles = (theme: ReactNativePaperTheme) => StyleSheet.create({
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.onSurfaceVariant,
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginTop: 16,
    marginBottom: 8,
    textTransform: 'uppercase',
  },
  card: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    elevation: 2,
    marginBottom: 16,
    overflow: 'hidden',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: 16,
  },
  infoBox: {
    padding: 12, 
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: 8,
    marginTop: 12,
  },
  infoText: {
    fontSize: 13, 
    color: theme.colors.onSurfaceVariant
  }
});

export default AppPreferencesSection; 