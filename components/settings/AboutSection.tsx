import React from 'react';
import { View, StyleSheet, Image } from 'react-native';
import { Card, Text, useTheme, type MD3Theme as ReactNativePaperTheme } from 'react-native-paper';
import Constants from 'expo-constants';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface AboutSectionProps {
  appName?: string;
  appDescription?: string;
}

const AboutSection: React.FC<AboutSectionProps> = ({
  appName = 'Gift Corner',
  appDescription = 'Billing application for your business'
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  
  const version = Constants.expoConfig?.version || '1.0.0';
  const buildNumber = Constants.expoConfig?.ios?.buildNumber || 
                      Constants.expoConfig?.android?.versionCode || 
                      '1';

  return (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <MaterialCommunityIcons name="store" size={48} color={theme.colors.primary} />
          </View>
          <View style={styles.appInfoContainer}>
            <Text style={styles.appName}>{appName}</Text>
            <Text style={styles.appDescription}>{appDescription}</Text>
          </View>
        </View>
        
        <View style={styles.infoContainer}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Version</Text>
            <Text style={styles.infoValue}>{version}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Build</Text>
            <Text style={styles.infoValue}>{buildNumber}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Platform</Text>
            <Text style={styles.infoValue}>{Constants.platform?.ios ? 'iOS' : 'Android'}</Text>
          </View>
        </View>
        
        <Text style={styles.copyright}>
          © 2025 {appName}. All rights reserved.
        </Text>
      </Card.Content>
    </Card>
  );
};

const createStyles = (theme: ReactNativePaperTheme) => StyleSheet.create({
  card: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    elevation: 2,
    marginBottom: 16,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  logoContainer: {
    width: 64,
    height: 64,
    borderRadius: 16,
    backgroundColor: theme.colors.primaryContainer,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  appInfoContainer: {
    flex: 1,
  },
  appName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: 4,
  },
  appDescription: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  infoContainer: {
    padding: 16,
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: 12,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.onSurfaceVariant,
  },
  infoValue: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  copyright: {
    fontSize: 13,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
  },
});

export default AboutSection; 