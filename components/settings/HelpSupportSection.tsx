import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Card, Text, useTheme, type MD3Theme as ReactNativePaperTheme } from 'react-native-paper';
import SettingRow from './SettingRow';

interface HelpSupportSectionProps {
  onPressUserGuide: () => void;
  onPressSendFeedback: () => void;
  onPressRateApp: () => void;
  onPressCheckUpdates: () => void;
}

const HelpSupportSection: React.FC<HelpSupportSectionProps> = ({
  onPressUserGuide,
  onPressSendFeedback,
  onPressRateApp,
  onPressCheckUpdates
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <>
      <Text style={styles.sectionTitle}>Help & Support</Text>
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Support Resources</Text>
          
          <SettingRow
            label="User Guide"
            description="Learn how to use all features"
            icon="book-open-variant"
            onPress={onPressUserGuide}
          />
          
          <SettingRow
            label="Check for Updates"
            description="Make sure you have the latest version"
            icon="update"
            onPress={onPressCheckUpdates}
          />
        </Card.Content>
      </Card>
      
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Feedback</Text>
          
          <SettingRow
            label="Send Feedback"
            description="Help us improve the app"
            icon="message-text-outline"
            onPress={onPressSendFeedback}
          />
          
          <SettingRow
            label="Rate App"
            description="Let others know what you think"
            icon="star-outline"
            onPress={onPressRateApp}
          />
          
          <View style={styles.infoBox}>
            <Text style={styles.infoText}>
              Your feedback helps us make the app better for everyone!
            </Text>
          </View>
        </Card.Content>
      </Card>
    </>
  );
};

const createStyles = (theme: ReactNativePaperTheme) => StyleSheet.create({
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.onSurfaceVariant,
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginTop: 16,
    marginBottom: 8,
    textTransform: 'uppercase',
  },
  card: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    elevation: 2,
    marginBottom: 16,
    overflow: 'hidden',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: 16,
  },
  infoBox: {
    padding: 12, 
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: 8,
    marginTop: 12,
  },
  infoText: {
    fontSize: 13, 
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
  },
});

export default HelpSupportSection; 