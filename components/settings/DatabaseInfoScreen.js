import React, { useEffect, useState, useCallback } from 'react';
import { View, StyleSheet, ScrollView, Alert, Text, TouchableOpacity } from 'react-native';
import { Card, Button, Divider, Title, Paragraph, List, ActivityIndicator } from 'react-native-paper';
import { useDatabase } from '@/hooks/useDatabase';
import { resetDatabase, getDatabaseMetrics, adapterInfo } from '@/database';
import { clearImageCache, getImageCacheInfo } from '@/utils/imageOptimization';

/**
 * Database Information Screen
 * Shows database metrics, adapter type, and allows cache management
 */
const DatabaseInfoScreen = () => {
  const { database, isReady } = useDatabase();
  const [metrics, setMetrics] = useState(null);
  const [imageCache, setImageCache] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshKey, setRefreshKey] = useState(0);
  
  // Load metrics
  useEffect(() => {
    const loadMetrics = async () => {
      try {
        setIsLoading(true);
        const dbMetrics = await getDatabaseMetrics();
        const imageCacheInfo = await getImageCacheInfo();
        
        setMetrics(dbMetrics);
        setImageCache(imageCacheInfo);
      } catch (error) {
        console.error('Error loading metrics:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    if (isReady) {
      loadMetrics();
    }
  }, [isReady, refreshKey]);
  
  // Refresh metrics
  const handleRefresh = useCallback(() => {
    setRefreshKey(prevKey => prevKey + 1);
  }, []);
  
  // Reset database with confirmation
  const handleResetDatabase = useCallback(() => {
    Alert.alert(
      'Reset Database',
      'This will delete ALL data from the database. This action cannot be undone. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reset', 
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);
              await resetDatabase();
              handleRefresh();
            } catch (error) {
              console.error('Error resetting database:', error);
              Alert.alert('Error', 'Failed to reset database.');
            } finally {
              setIsLoading(false);
            }
          }
        }
      ]
    );
  }, [handleRefresh]);
  
  // Clear image cache
  const handleClearImageCache = useCallback(async () => {
    try {
      setIsLoading(true);
      await clearImageCache();
      const imageCacheInfo = await getImageCacheInfo();
      setImageCache(imageCacheInfo);
    } catch (error) {
      console.error('Error clearing image cache:', error);
      Alert.alert('Error', 'Failed to clear image cache.');
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  if (!isReady || isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading database info...</Text>
      </View>
    );
  }
  
  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <Card style={styles.card}>
        <Card.Content>
          <Title>Database Adapter</Title>
          <List.Item
            title="Adapter Type"
            description={adapterInfo.type}
            left={props => <List.Icon {...props} icon="database" />}
          />
          <List.Item
            title="JSI Enabled"
            description={adapterInfo.jsiEnabled ? 'Yes' : 'No'}
            left={props => <List.Icon {...props} icon="flash" />}
          />
          <List.Item
            title="Debug Mode"
            description={adapterInfo.debugMode ? 'Yes' : 'No'}
            left={props => <List.Icon {...props} icon="bug" />}
          />
          <List.Item
            title="Platform"
            description={adapterInfo.platform}
            left={props => <List.Icon {...props} icon="cellphone" />}
          />
          
          <Paragraph style={styles.adapterDescription}>
            {adapterInfo.type === 'SQLite' && adapterInfo.jsiEnabled 
              ? 'SQLite with JSI provides maximum performance for production use.'
              : 'LokiJS is being used during development with remote debugging enabled. For maximum performance, disable remote debugging to use SQLite with JSI.'}
          </Paragraph>
        </Card.Content>
      </Card>
      
      {metrics && (
        <Card style={styles.card}>
          <Card.Content>
            <Title>Data Metrics</Title>
            <View style={styles.metricsContainer}>
              <View style={styles.metricItem}>
                <Text style={styles.metricValue}>{metrics.counts.bills}</Text>
                <Text style={styles.metricLabel}>Bills</Text>
              </View>
              <View style={styles.metricItem}>
                <Text style={styles.metricValue}>{metrics.counts.customers}</Text>
                <Text style={styles.metricLabel}>Customers</Text>
              </View>
              <View style={styles.metricItem}>
                <Text style={styles.metricValue}>{metrics.counts.items}</Text>
                <Text style={styles.metricLabel}>Items</Text>
              </View>
              <View style={styles.metricItem}>
                <Text style={styles.metricValue}>{metrics.counts.payments}</Text>
                <Text style={styles.metricLabel}>Payments</Text>
              </View>
            </View>
            <View style={styles.totalContainer}>
              <Text style={styles.totalLabel}>Total Records:</Text>
              <Text style={styles.totalValue}>{metrics.counts.total}</Text>
            </View>
          </Card.Content>
        </Card>
      )}
      
      {imageCache && (
        <Card style={styles.card}>
          <Card.Content>
            <Title>Image Cache</Title>
            <List.Item
              title="Cached Images"
              description={`${imageCache.count} files`}
              left={props => <List.Icon {...props} icon="image" />}
            />
            <List.Item
              title="Total Size"
              description={imageCache.formattedSize}
              left={props => <List.Icon {...props} icon="folder" />}
            />
          </Card.Content>
          <Card.Actions>
            <Button onPress={handleClearImageCache}>Clear Image Cache</Button>
          </Card.Actions>
        </Card>
      )}
      
      <Card style={[styles.card, styles.dangerCard]}>
        <Card.Content>
          <Title style={styles.dangerTitle}>Danger Zone</Title>
          <Paragraph style={styles.dangerText}>
            These actions can cause data loss and should only be used during development.
          </Paragraph>
        </Card.Content>
        <Card.Actions>
          <Button 
            mode="contained" 
            buttonColor="#d32f2f"
            textColor="#ffffff"
            onPress={handleResetDatabase}
          >
            Reset Database
          </Button>
        </Card.Actions>
      </Card>
      
      <View style={styles.refreshContainer}>
        <Button 
          mode="contained" 
          icon="refresh" 
          onPress={handleRefresh}
        >
          Refresh Metrics
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  card: {
    marginBottom: 16,
  },
  adapterDescription: {
    marginTop: 8,
    fontStyle: 'italic',
    color: '#666',
  },
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  metricItem: {
    alignItems: 'center',
    flex: 1,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  metricLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 24,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  totalValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  refreshContainer: {
    marginTop: 8,
    marginBottom: 24,
  },
  dangerCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#d32f2f',
  },
  dangerTitle: {
    color: '#d32f2f',
  },
  dangerText: {
    marginBottom: 8,
  },
});

export default DatabaseInfoScreen; 