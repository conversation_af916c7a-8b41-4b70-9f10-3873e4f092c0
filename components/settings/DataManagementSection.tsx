import React from 'react';
import { View, StyleSheet } from 'react-native';
import { <PERSON><PERSON>, <PERSON>, Chip, Divider, RadioButton, SegmentedButtons, Text, useTheme, type MD3Theme as ReactNativePaperTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface DataManagementSectionProps {
  billsCount: number;
  exportFormat: 'csv' | 'json';
  selectedExportOption: string;
  dateRangeText: string;
  isExporting: boolean;
  isImporting: boolean;
  isClearing: boolean;
  isBackingUp: boolean;
  isRestoring: boolean;
  onExportFormatChange: (value: 'csv' | 'json') => void;
  onExportOptionChange: (value: string) => void;
  onSelectDateRange: () => void;
  onExportBills: () => void;
  onImportBills: () => void;
  onClearData: () => void;
  onBackupData: () => void;
  onRestoreData: () => void;
}

const DataManagementSection: React.FC<DataManagementSectionProps> = ({
  billsCount,
  exportFormat,
  selectedExportOption,
  dateRangeText,
  isExporting,
  isImporting,
  isClearing,
  isBackingUp,
  isRestoring,
  onExportFormatChange,
  onExportOptionChange,
  onSelectDateRange,
  onExportBills,
  onImportBills,
  onClearData,
  onBackupData,
  onRestoreData
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  
  const isProcessing = isExporting || isImporting || isClearing || isBackingUp || isRestoring;

  return (
    <>
      <Text style={styles.sectionTitle}>Data Management</Text>
      
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Backup & Restore</Text>
          
          <View style={styles.chipContainer}>
            <Chip 
              style={styles.statsChip} 
              icon="file-cabinet" 
              mode="flat"
            >
              {billsCount} Bills Stored
            </Chip>
          </View>
          
          <View style={styles.buttonRow}>
            <Button 
              mode="outlined" 
              icon="database-export" 
              onPress={onBackupData}
              style={styles.halfButton}
              loading={isBackingUp}
              disabled={isProcessing}
            >
              Backup
            </Button>
            <Button 
              mode="outlined" 
              icon="database-import" 
              onPress={onRestoreData}
              style={styles.halfButton}
              loading={isRestoring}
              disabled={isProcessing}
            >
              Restore
            </Button>
          </View>
          
          <Text style={styles.helperText}>
            Backup includes all bills and app settings
          </Text>
        </Card.Content>
      </Card>
      
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Export Options</Text>
          
          <SegmentedButtons
            style={styles.segment}
            value={exportFormat}
            onValueChange={(value) => onExportFormatChange(value as 'csv' | 'json')}
            buttons={[
              { value: 'csv', label: 'CSV', icon: 'file-delimited-outline' },
              { value: 'json', label: 'JSON', icon: 'code-json' },
            ]}
          />
          
          <RadioButton.Group 
            value={selectedExportOption} 
            onValueChange={onExportOptionChange}
          >
            <View style={styles.radioGroup}>
              <RadioButton.Item 
                label="Export All Bills" 
                value="all" 
                labelStyle={styles.radioLabel}
              />
              <RadioButton.Item 
                label="Export By Date Range" 
                value="date_range" 
                labelStyle={styles.radioLabel}
              />
              
              {selectedExportOption === 'date_range' && (
                <View style={styles.dateFilterContainer}>
                  <Button 
                    mode="outlined" 
                    icon="calendar-range" 
                    onPress={onSelectDateRange}
                    style={styles.dateRangeButton}
                  >
                    Select Date Range
                  </Button>
                  <Text style={styles.dateRangeText}>
                    {dateRangeText}
                  </Text>
                </View>
              )}
            </View>
          </RadioButton.Group>
          
          <Button 
            mode="contained" 
            icon="export-variant" 
            onPress={onExportBills}
            style={styles.button}
            loading={isExporting}
            disabled={isProcessing || (selectedExportOption === 'date_range' && !dateRangeText)}
          >
            {isExporting ? 'Exporting...' : `Export Bills`}
          </Button>
        </Card.Content>
      </Card>
      
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Import & Advanced</Text>
          
          <Button 
            mode="outlined" 
            icon="database-import" 
            onPress={onImportBills}
            style={styles.button}
            loading={isImporting}
            disabled={isProcessing}
          >
            Import JSON Data
          </Button>
          
          <Divider style={styles.divider} />
          
          <View style={styles.dangerSection}>
            <Text style={styles.dangerText}>Danger Zone</Text>
            <Button 
              mode="outlined" 
              icon="delete-sweep" 
              onPress={onClearData}
              style={[styles.button, {borderColor: theme.colors.error}]}
              textColor={theme.colors.error}
              disabled={isProcessing}
            >
              Delete All Bills
            </Button>
            <Text style={[styles.helperText, {color: theme.colors.error}]}>
              This action cannot be undone
            </Text>
          </View>
        </Card.Content>
      </Card>
    </>
  );
};

const createStyles = (theme: ReactNativePaperTheme) => StyleSheet.create({
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.onSurfaceVariant,
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginTop: 16,
    marginBottom: 8,
    textTransform: 'uppercase',
  },
  card: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    elevation: 2,
    marginBottom: 16,
    overflow: 'hidden',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: 16,
  },
  chipContainer: {
    flexDirection: 'row',
    marginBottom: 16, 
  },
  statsChip: {
    marginRight: 8,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  halfButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  button: {
    marginVertical: 8,
  },
  helperText: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginTop: 4,
  },
  divider: {
    marginVertical: 16,
  },
  segment: {
    marginBottom: 16,
  },
  radioGroup: {
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: 12,
    marginBottom: 16,
    padding: 8,
  },
  radioLabel: {
    color: theme.colors.onSurface
  },
  dateFilterContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  dateRangeButton: {
    marginBottom: 8,
  },
  dateRangeText: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  dangerSection: {
    backgroundColor: theme.colors.errorContainer,
    borderRadius: 12,
    padding: 16,
  },
  dangerText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.error,
    marginBottom: 12,
    textAlign: 'center',
  },
});

export default DataManagementSection; 