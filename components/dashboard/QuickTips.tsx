import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Divider, Text, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface Tip {
  icon: keyof typeof MaterialCommunityIcons.glyphMap;
  text: string;
}

interface QuickTipsProps {
  tips?: Tip[];
}

const defaultTips: Tip[] = [
  {
    icon: 'lightbulb-on-outline',
    text: 'Engage with customers to understand their needs for better recommendations.'
  },
  {
    icon: 'backup-restore',
    text: 'Regularly export your bill history from settings as a backup.'
  },
  {
    icon: 'trending-up',
    text: 'Monitor your popular items to optimize stock and promotions.'
  }
];

const QuickTips: React.FC<QuickTipsProps> = ({ tips = defaultTips }) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <>
      {tips.map((tip, index) => (
        <React.Fragment key={index}>
          {index > 0 && <Divider style={styles.divider} />}
          <View style={styles.tipRow}>
            <MaterialCommunityIcons 
              name={tip.icon} 
              size={20} 
              color={theme.colors.onTertiaryContainer} 
              style={styles.tipIcon}
            />
            <Text style={[
              styles.tipText, 
              index === 0 ? { fontWeight: 'bold' } : {}
            ]}>
              {tip.text}
            </Text>
          </View>
        </React.Fragment>
      ))}
    </>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  tipRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 8,
  },
  tipIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  tipText: {
    flex: 1,
    color: theme.colors.onTertiaryContainer,
    lineHeight: 20,
  },
  divider: {
    backgroundColor: theme.colors.onTertiaryContainer,
    opacity: 0.2,
  }
});

export default QuickTips; 