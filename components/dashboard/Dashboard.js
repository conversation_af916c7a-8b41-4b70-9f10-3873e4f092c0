import React, { useCallback, useEffect, useState, useMemo, memo } from 'react';
import { View, StyleSheet, Text, Dimensions, Platform } from 'react-native';
import { Card, Button, useTheme } from 'react-native-paper';
import { withDatabase } from '@nozbe/watermelondb/DatabaseProvider';
import withObservables from '@nozbe/with-observables';
import { Q } from '@nozbe/watermelondb';
import { format } from 'date-fns';

import { BillService } from '@/utils/databaseService';
import OptimizedList from '@/components/ui/OptimizedList';
import { createItemRenderer, memoWithKeys } from '@/utils/componentOptimization';

// Memoized bill item component with specific prop comparison
const BillItem = memoWithKeys(({ item, onPress }) => {
  // Don't re-calculate these values on every render
  const formattedDate = useMemo(() => {
    return format(new Date(item.date), 'MMM dd, yyyy');
  }, [item.date]);
  
  const statusColor = useMemo(() => {
    return item.status === 'paid' ? '#2e7d32' : '#ff8f00';
  }, [item.status]);
  
  const statusBgColor = useMemo(() => {
    return item.status === 'paid' ? '#e6f7e6' : '#fff8e1';
  }, [item.status]);
  
  // Handlers
  const handlePress = useCallback(() => {
    onPress(item.id);
  }, [onPress, item.id]);
  
  return (
    <Card style={styles.card} mode="outlined">
      <Card.Content>
        <View style={styles.billHeader}>
          <Text style={styles.billNumber}>#{item.billNumber}</Text>
          <View style={[styles.statusContainer, { backgroundColor: statusBgColor }]}>
            <Text style={[styles.statusText, { color: statusColor }]}>
              {item.status.toUpperCase()}
            </Text>
          </View>
        </View>
        
        <Text style={styles.date}>{formattedDate}</Text>
        <Text style={styles.amount}>₹{item.totalAmount.toFixed(2)}</Text>
      </Card.Content>
      <Card.Actions>
        <Button onPress={handlePress}>View Details</Button>
      </Card.Actions>
    </Card>
  );
}, ['item.id', 'item.billNumber', 'item.status', 'item.date', 'item.totalAmount', 'onPress']);

// Create optimized renderer for FlatList
const renderBill = createItemRenderer(BillItem);

// Stats card component
const StatsCard = memo(({ title, value, subtitle, icon, color }) => {
  const theme = useTheme();
  
  return (
    <Card style={[styles.statsCard, { borderLeftColor: color || theme.colors.primary }]}>
      <Card.Content>
        <Text style={styles.statsTitle}>{title}</Text>
        <Text style={[styles.statsValue, { color: color || theme.colors.primary }]}>{value}</Text>
        {subtitle && <Text style={styles.statsSubtitle}>{subtitle}</Text>}
      </Card.Content>
    </Card>
  );
});

// Main dashboard component
const Dashboard = ({ bills, navigation }) => {
  const [stats, setStats] = useState({
    pending: { count: 0, amount: 0 },
    paid: { count: 0, amount: 0 },
    total: 0
  });
  
  // Calculate stats based on bills
  useEffect(() => {
    const calculateStats = async () => {
      try {
        const statsData = await BillService.getStatusStats();
        setStats(statsData);
      } catch (error) {
        console.error('Error calculating stats:', error);
      }
    };
    
    calculateStats();
  }, [bills]);
  
  // Format currency for display
  const formatCurrency = useCallback((amount) => {
    return `₹${amount.toFixed(2)}`;
  }, []);
  
  // Recent bills (last 7 days)
  const recentBills = useMemo(() => {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    return bills.filter(bill => new Date(bill.date) >= sevenDaysAgo);
  }, [bills]);
  
  // Handle bill selection
  const handleBillPress = useCallback((billId) => {
    navigation.navigate('BillDetails', { billId });
  }, [navigation]);
  
  // Render stats section
  const renderStatsSection = useMemo(() => (
    <View style={styles.statsContainer}>
      <StatsCard 
        title="Total Bills" 
        value={stats.total} 
        subtitle="All time"
        color="#3f51b5"
      />
      <StatsCard 
        title="Pending" 
        value={stats.pending.count} 
        subtitle={formatCurrency(stats.pending.amount)}
        color="#ff8f00"
      />
      <StatsCard 
        title="Paid" 
        value={stats.paid.count} 
        subtitle={formatCurrency(stats.paid.amount)}
        color="#2e7d32"
      />
    </View>
  ), [stats, formatCurrency]);
  
  return (
    <View style={styles.container}>
      <OptimizedList
        data={recentBills}
        renderItem={({ item }) => renderBill({ item, onPress: handleBillPress })}
        keyExtractor={item => item.id}
        ListHeaderComponent={
          <>
            <Text style={styles.headerTitle}>Dashboard</Text>
            {renderStatsSection}
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Recent Bills</Text>
              <Button 
                mode="text" 
                onPress={() => navigation.navigate('Bills')}
              >
                View All
              </Button>
            </View>
          </>
        }
        contentContainerStyle={styles.listContent}
        initialNumToRender={5}
        maxToRenderPerBatch={5}
        windowSize={3}
        itemHeight={150} // Approximate height for getItemLayout
      />
    </View>
  );
};

// Enhance component with database observables
const enhanceWithObservables = withObservables([], ({ database }) => ({
  // Observe bills collection with specific queries
  bills: database.get('bills').query(
    Q.or(
      Q.where('status', 'pending'),
      Q.where('date', Q.gte(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)))
    ),
    Q.sortBy('date', 'desc')
  ).observe()
}));

// Get screen dimensions for responsive design
const { width } = Dimensions.get('window');
const isTablet = width > 768;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 16,
    marginHorizontal: 16,
    marginTop: 16,
  },
  listContent: {
    padding: 16,
  },
  statsContainer: {
    flexDirection: isTablet ? 'row' : 'column',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  statsCard: {
    marginBottom: 12,
    flex: isTablet ? 1 : undefined,
    marginHorizontal: isTablet ? 4 : 0,
    borderLeftWidth: 4,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  statsTitle: {
    fontSize: 14,
    color: '#666',
  },
  statsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginVertical: 4,
  },
  statsSubtitle: {
    fontSize: 12,
    color: '#888',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  card: {
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  billHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  billNumber: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  statusContainer: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  date: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  amount: {
    fontSize: 18,
    fontWeight: 'bold',
  },
});

// Export the enhanced component
export default withDatabase(enhanceWithObservables(Dashboard)); 