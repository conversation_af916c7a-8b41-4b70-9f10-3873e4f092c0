import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Card, Text, useTheme, Surface } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface SummaryCardProps {
  icon: keyof typeof MaterialCommunityIcons.glyphMap;
  value: string | number;
  title: string;
  color?: string;
  iconBackground?: string;
}

const screenWidth = Dimensions.get('window').width;
const cardWidth = (screenWidth - 48) / 2;

const SummaryCard: React.FC<SummaryCardProps> = ({ 
  icon, 
  value, 
  title, 
  color, 
  iconBackground 
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <Surface style={styles.summaryCard}>
      <View style={styles.cardContent}>
        <View style={[styles.iconContainer, {backgroundColor: iconBackground}]}>
          <MaterialCommunityIcons 
            name={icon} 
            size={24} 
            color={color || theme.colors.primary} 
          />
        </View>
        <Text style={styles.cardValue}>{value}</Text>
        <Text style={styles.cardTitle}>{title}</Text>
      </View>
    </Surface>
  );
};

interface SummaryCardsProps {
  totalSales: number;
  monthlySales: number;
  weeklySales: number;
  averageBill: number;
  totalBills: number;
  monthlyBills: number;
  weeklyBills: number;
  currencySymbol?: string;
}

const SummaryCards: React.FC<SummaryCardsProps> = ({
  totalSales,
  monthlySales,
  weeklySales,
  averageBill,
  totalBills,
  monthlyBills,
  weeklyBills,
  currencySymbol = '₹'
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <>
      <View style={styles.cardsContainer}>
        <SummaryCard
          icon="cash-multiple"
          value={`${currencySymbol}${totalSales.toFixed(0)}`}
          title="Total Sales"
          color={theme.colors.primary}
          iconBackground="rgba(25, 118, 210, 0.1)"
        />
        <SummaryCard
          icon="calendar-month"
          value={`${currencySymbol}${monthlySales.toFixed(0)}`}
          title="This Month"
          color="#4CAF50"
          iconBackground="rgba(76, 175, 80, 0.1)"
        />
        <SummaryCard
          icon="calendar-week"
          value={`${currencySymbol}${weeklySales.toFixed(0)}`}
          title="This Week"
          color="#9C27B0"
          iconBackground="rgba(156, 39, 176, 0.1)"
        />
        <SummaryCard
          icon="scale-balance"
          value={`${currencySymbol}${averageBill.toFixed(0)}`}
          title="Avg. Bill Value"
          color="#FF9800"
          iconBackground="rgba(255, 152, 0, 0.1)"
        />
      </View>

      <View style={styles.horizontalCards}>
        <Surface style={styles.horizontalCard}>
          <View style={styles.horizontalCardContent}>
            <View style={styles.horizontalCardLeft}>
              <View style={[styles.iconContainer, {backgroundColor: "rgba(25, 118, 210, 0.1)"}]}>
                <MaterialCommunityIcons 
                  name="file-document-multiple-outline" 
                  size={24} 
                  color={theme.colors.primary} 
                />
              </View>
              <Text style={styles.horizontalCardTitle}>Total Bills</Text>
            </View>
            <Text style={styles.horizontalCardValue}>{totalBills}</Text>
          </View>
        </Surface>
        
        <View style={styles.horizontalCardsRow}>
          <Surface style={[styles.horizontalCardSmall, {marginRight: 8}]}>
            <View style={styles.horizontalCardContent}>
              <Text style={styles.smallCardTitle}>Monthly</Text>
              <Text style={styles.smallCardValue}>{monthlyBills}</Text>
            </View>
          </Surface>
          
          <Surface style={styles.horizontalCardSmall}>
            <View style={styles.horizontalCardContent}>
              <Text style={styles.smallCardTitle}>Weekly</Text>
              <Text style={styles.smallCardValue}>{weeklyBills}</Text>
            </View>
          </Surface>
        </View>
      </View>
    </>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  cardsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  summaryCard: {
    width: cardWidth,
    marginBottom: 16,
    borderRadius: 16,
    elevation: 2,
    overflow: 'hidden',
    backgroundColor: theme.colors.surface,
  },
  cardContent: {
    alignItems: 'flex-start',
    padding: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
    marginBottom: 4,
  },
  cardTitle: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  horizontalCards: {
    marginBottom: 16,
  },
  horizontalCard: {
    borderRadius: 16,
    elevation: 2,
    overflow: 'hidden',
    marginBottom: 8,
    backgroundColor: theme.colors.surface,
  },
  horizontalCardContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  horizontalCardLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  horizontalCardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.onSurface,
    marginLeft: 12,
  },
  horizontalCardValue: {
    fontSize: 22,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  horizontalCardsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  horizontalCardSmall: {
    flex: 1,
    borderRadius: 16,
    elevation: 2,
    overflow: 'hidden',
    backgroundColor: theme.colors.surface,
  },
  smallCardTitle: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  smallCardValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
    marginTop: 4,
  }
});

export default SummaryCards; 