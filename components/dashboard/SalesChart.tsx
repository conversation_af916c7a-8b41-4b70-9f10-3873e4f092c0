import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Card, Text, useTheme } from 'react-native-paper';
import { LineChart } from 'react-native-chart-kit';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { ChartData } from './AnalyticsProcessor';

interface SalesChartProps {
  chartData: ChartData;
  currencySymbol?: string;
}

const screenWidth = Dimensions.get("window").width;

const SalesChart: React.FC<SalesChartProps> = ({
  chartData,
  currencySymbol = '₹'
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  const hasData = chartData && 
                  chartData.labels.length > 0 && 
                  chartData.datasets[0].data.some(d => d > 0);

  // Chart configuration
  const chartConfig = {
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(${hexToRgb(theme.colors.primary)}, ${opacity})`,
    labelColor: () => theme.colors.onSurfaceVariant,
    style: { borderRadius: 16 },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: theme.colors.primary,
    },
    propsForBackgroundLines: {
      strokeDasharray: '', // solid background lines
      stroke: theme.dark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
    },
  };

  return (
    <>
      <Text style={styles.sectionTitle}>Sales Trend</Text>
      {hasData ? (
        <View style={styles.chartContainer}>
          <LineChart
            data={chartData}
            width={screenWidth - 32}
            height={220}
            yAxisLabel={currencySymbol}
            yAxisSuffix=""
            chartConfig={chartConfig}
            bezier
            style={{
              marginVertical: 8,
              borderRadius: 16,
            }}
          />
        </View>
      ) : (
        <Card style={styles.noDataCard}>
          <Card.Content style={styles.noDataContent}>
            <MaterialCommunityIcons 
              name="chart-bar-stacked" 
              size={32} 
              color={theme.colors.onSurfaceVariant} 
              style={styles.noDataIcon}
            />
            <Text style={styles.noDataText}>Not enough data for sales trend chart.</Text>
            <Text style={styles.noDataSubtext}>Create more bills to see trends.</Text>
          </Card.Content>
        </Card>
      )}
    </>
  );
};

// Helper to convert hex to rgb
const hexToRgb = (hex: string): string => {
  // Remove # if present
  hex = hex.replace('#', '');
  
  // Convert 3-digit hex to 6-digits
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }
  
  // Parse the hex values
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  
  return `${r},${g},${b}`;
};

const createStyles = (theme: any) => StyleSheet.create({
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.onSurfaceVariant,
    marginTop: 20,
    marginBottom: 12,
  },
  chartContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  noDataCard: {
    borderRadius: 16,
    marginBottom: 16,
  },
  noDataContent: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  noDataIcon: {
    marginBottom: 8,
  },
  noDataText: {
    color: theme.colors.onSurfaceVariant,
  },
  noDataSubtext: {
    fontSize: 12,
    color: theme.colors.onSurfaceDisabled,
  },
});

export default SalesChart; 