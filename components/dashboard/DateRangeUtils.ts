import { DateFilterOption } from './DateFilterSelector';

export interface DateRange {
  start: Date | undefined;
  end: Date | undefined;
}

/**
 * Calculate date range based on filter option
 */
export const getFilteredDateRange = (
  filterOption: DateFilterOption,
  customRange: { startDate: Date | undefined; endDate: Date | undefined }
): DateRange => {
  const today = new Date();
  today.setHours(23, 59, 59, 999); // End of today
  
  const startOfToday = new Date(today);
  startOfToday.setHours(0, 0, 0, 0); // Start of today
  
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  yesterday.setHours(0, 0, 0, 0); // Start of yesterday
  
  const endOfYesterday = new Date(today);
  endOfYesterday.setDate(endOfYesterday.getDate() - 1);
  endOfYesterday.setHours(23, 59, 59, 999); // End of yesterday
  
  const startOfThisWeek = new Date(today);
  startOfThisWeek.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1)); // Set to Monday
  startOfThisWeek.setHours(0, 0, 0, 0);
  
  const endOfLastWeek = new Date(startOfThisWeek);
  endOfLastWeek.setDate(endOfLastWeek.getDate() - 1);
  endOfLastWeek.setHours(23, 59, 59, 999);
  
  const startOfLastWeek = new Date(startOfThisWeek);
  startOfLastWeek.setDate(startOfLastWeek.getDate() - 7);
  startOfLastWeek.setHours(0, 0, 0, 0);
  
  const startOfThisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  startOfThisMonth.setHours(0, 0, 0, 0);
  
  const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
  startOfLastMonth.setHours(0, 0, 0, 0);
  
  const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
  endOfLastMonth.setHours(23, 59, 59, 999);
  
  switch (filterOption) {
    case DateFilterOption.TODAY:
      return { start: startOfToday, end: today };
    case DateFilterOption.YESTERDAY:
      return { start: yesterday, end: endOfYesterday };
    case DateFilterOption.THIS_WEEK:
      return { start: startOfThisWeek, end: today };
    case DateFilterOption.LAST_WEEK:
      return { start: startOfLastWeek, end: endOfLastWeek };
    case DateFilterOption.THIS_MONTH:
      return { start: startOfThisMonth, end: today };
    case DateFilterOption.LAST_MONTH:
      return { start: startOfLastMonth, end: endOfLastMonth };
    case DateFilterOption.CUSTOM:
      return { 
        start: customRange.startDate, 
        end: customRange.endDate ? 
          (() => {
            // Set end date to end of day
            const endDate = new Date(customRange.endDate);
            endDate.setHours(23, 59, 59, 999);
            return endDate;
          })() : 
          today 
      };
    case DateFilterOption.ALL_TIME:
    default:
      return { start: undefined, end: undefined };
  }
};

/**
 * Get common time ranges for analytics
 */
export const getCommonDateRanges = (): { 
  startOfToday: Date, 
  startOfWeek: Date, 
  startOfMonth: Date 
} => {
  const today = new Date();
  
  const startOfToday = new Date(today);
  startOfToday.setHours(0, 0, 0, 0);
  
  const startOfWeek = new Date(today);
  startOfWeek.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1)); // Set to Monday
  startOfWeek.setHours(0, 0, 0, 0);
  
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  startOfMonth.setHours(0, 0, 0, 0);
  
  return { startOfToday, startOfWeek, startOfMonth };
}; 