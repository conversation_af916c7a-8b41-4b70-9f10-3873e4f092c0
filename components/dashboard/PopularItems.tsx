import React from 'react';
import { StyleSheet, View } from 'react-native';
import { DataTable, Text, useTheme, Divider } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface PopularItem {
  name: string;
  count: number;
}

interface PopularItemsProps {
  items: PopularItem[];
}

const PopularItems: React.FC<PopularItemsProps> = ({ items }) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  const topItems = items.slice(0, 5);

  if (items.length === 0) {
    return (
      <View style={styles.noDataContainer}>
        <MaterialCommunityIcons 
          name="cart-outline" 
          size={24} 
          color={theme.colors.onSurfaceVariant} 
          style={styles.noDataIcon} 
        />
        <Text style={styles.noDataText}>No item data to show</Text>
      </View>
    );
  }

  return (
    <View>
      {topItems.map((item, index) => (
        <React.Fragment key={`${item.name}-${index}`}>
          {index > 0 && <Divider style={styles.divider} />}
          <View style={styles.itemRow}>
            <View style={styles.nameContainer}>
              <View style={styles.itemBadge}>
                <Text style={styles.itemBadgeText}>{index + 1}</Text>
              </View>
              <Text style={styles.itemName} numberOfLines={1} ellipsizeMode="tail">
                {item.name}
              </Text>
            </View>
            <View style={styles.countContainer}>
              <Text style={styles.itemCount}>{item.count}</Text>
              <Text style={styles.countLabel}>sold</Text>
            </View>
          </View>
        </React.Fragment>
      ))}
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  divider: {
    marginVertical: 8,
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primaryContainer,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  itemBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: theme.colors.onPrimaryContainer,
  },
  itemName: {
    fontSize: 15,
    color: theme.colors.onSurface,
    flex: 1,
  },
  countContainer: {
    alignItems: 'flex-end',
  },
  itemCount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  countLabel: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
  },
  noDataContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 24,
  },
  noDataIcon: {
    marginBottom: 8,
  },
  noDataText: {
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
  }
});

export default PopularItems; 