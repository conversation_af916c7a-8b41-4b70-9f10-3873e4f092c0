import { DateRange } from './DateRangeUtils';
import { getCommonDateRanges } from './DateRangeUtils';
import { useTheme } from 'react-native-paper';

export interface Bill {
  id: string;
  date: string;
  customerName?: string;
  customerContact?: string;
  items: {
    id?: string;
    name: string;
    price: number;
    quantity: number;
  }[];
  subtotal?: number;
  discount?: number;
  tax?: number;
  totalAmount: number;
  finalAmount: number;
  notes?: string;
  paymentMethod?: string;
}

export interface ChartData {
  labels: string[];
  datasets: [
    {
      data: number[];
      color?: (opacity: number) => string;
      strokeWidth?: number;
    }
  ];
  legend?: string[];
}

export interface AnalyticsData {
  totalOverallSales: number;
  totalMonthlySales: number;
  totalWeeklySales: number;
  overallBillCount: number;
  monthlyBillCount: number;
  weeklyBillCount: number;
  averageBillAmount: number;
  popularItems: { name: string; count: number }[];
  salesTrendData: ChartData;
}

/**
 * Process bills into analytics data
 */
export const processAnalyticsData = (
  bills: Bill[],
  dateRange: DateRange,
  colorCallback: (opacity: number) => string,
  filterLabel: string
): AnalyticsData => {
  // Create empty analytics data
  const emptyAnalytics: AnalyticsData = {
    totalOverallSales: 0,
    totalMonthlySales: 0,
    totalWeeklySales: 0,
    overallBillCount: 0,
    monthlyBillCount: 0,
    weeklyBillCount: 0,
    averageBillAmount: 0,
    popularItems: [],
    salesTrendData: { labels: [], datasets: [{ data: [] }] },
  };

  if (!bills || bills.length === 0) {
    return emptyAnalytics;
  }

  // Get common date ranges for weekly/monthly calculations
  const { startOfMonth, startOfWeek } = getCommonDateRanges();
  
  let totalOverallSales = 0;
  let totalMonthlySales = 0;
  let totalWeeklySales = 0;
  let monthlyBillCount = 0;
  let weeklyBillCount = 0;
  
  const itemCounts: { [key: string]: number } = {};
  
  // For chart: determine date range for X-axis
  const chartDays = 7; // Default to 7 days
  const dailySales: { [key: string]: number } = {}; 
  
  // If using custom date or predefined range, adjust chart to show appropriate period
  if (dateRange.start && dateRange.end) {
    const diffTime = Math.abs(dateRange.end.getTime() - dateRange.start.getTime());
    const diffDays = Math.min(Math.ceil(diffTime / (1000 * 60 * 60 * 24)), 30); // Cap at 30 days
    
    // Initialize sales data for each day in the range (up to diffDays)
    for (let i = 0; i < diffDays; i++) {
      const d: Date = new Date(dateRange.end);
      d.setDate(d.getDate() - i);
      // Format as YYYY-MM-DD for consistent keys
      const dateStr = d.toISOString().split('T')[0];
      dailySales[dateStr] = 0;
    }
  } else {
    // Default: Show last 7 days
    for (let i = 0; i < chartDays; i++) {
      const d: Date = new Date();
      d.setDate(d.getDate() - i);
      // Format as YYYY-MM-DD for consistent keys
      const dateStr = d.toISOString().split('T')[0];
      dailySales[dateStr] = 0;
    }
  }
  
  // Process bills into the chart data
  bills.forEach(bill => {
    totalOverallSales += bill.finalAmount || bill.totalAmount;
    
    const billDate = new Date(bill.date);
    const billDateStr = billDate.toISOString().split('T')[0]; // YYYY-MM-DD
    
    // Add to the appropriate date bucket for the chart if within range
    if (dailySales.hasOwnProperty(billDateStr)) {
      dailySales[billDateStr] += bill.finalAmount || bill.totalAmount;
    } else if (dateRange.start && dateRange.end) {
      // If we're using a date range, make sure to include all bills within that range
      // even if we didn't pre-populate the dailySales object with all possible dates
      if (billDate >= dateRange.start && billDate <= dateRange.end) {
        dailySales[billDateStr] = (dailySales[billDateStr] || 0) + (bill.finalAmount || bill.totalAmount);
      }
    }
    
    // Handle monthly and weekly counts/sums 
    if (billDate >= startOfMonth) {
      totalMonthlySales += bill.finalAmount || bill.totalAmount;
      monthlyBillCount++;
    }
    
    if (billDate >= startOfWeek) {
      totalWeeklySales += bill.finalAmount || bill.totalAmount;
      weeklyBillCount++;
    }
    
    // Count popular items
    bill.items.forEach(item => {
      const itemName = item.name || 'Unnamed Item';
      itemCounts[itemName] = (itemCounts[itemName] || 0) + item.quantity;
    });
  });
  
  // Sort dates in ascending order (for the chart)
  const sortedDates = Object.keys(dailySales).sort();
  const chartLabels: string[] = [];
  const chartData: number[] = [];
  
  // Process sorted dates for chart display
  sortedDates.forEach(dateStr => {
    // Format date for display (DD/MM format)
    const dateObj = new Date(dateStr);
    // Use more readable date format - DD/MM
    const day = dateObj.getDate().toString().padStart(2, '0');
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    chartLabels.push(`${day}/${month}`);
    chartData.push(dailySales[dateStr]);
  });
  
  // Format chart data with better colors and visualization
  const salesTrendData: ChartData = {
    labels: chartLabels,
    datasets: [{
      data: chartData,
      color: colorCallback,
      strokeWidth: 3,
    }],
    legend: ['Sales Trend']
  };

  const overallBillCount = bills.length;
  const averageBillAmount = overallBillCount > 0 ? totalOverallSales / overallBillCount : 0;
  
  const popularItems = Object.entries(itemCounts)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);

  return {
    totalOverallSales,
    totalMonthlySales,
    totalWeeklySales,
    overallBillCount,
    monthlyBillCount,
    weeklyBillCount,
    averageBillAmount,
    popularItems,
    salesTrendData,
  };
}; 