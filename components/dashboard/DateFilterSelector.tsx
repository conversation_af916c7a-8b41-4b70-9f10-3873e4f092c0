import React, { useState } from 'react';
import { Dimensions, View, StyleSheet } from 'react-native';
import { But<PERSON>, <PERSON><PERSON>, Divider, IconButton, Text, useTheme } from 'react-native-paper';
import { DatePickerModal } from 'react-native-paper-dates';
import { MaterialCommunityIcons } from '@expo/vector-icons';

// Pre-defined date filter options
export enum DateFilterOption {
  ALL_TIME = 'all_time',
  TODAY = 'today',
  YESTERDAY = 'yesterday',
  THIS_WEEK = 'this_week',
  LAST_WEEK = 'last_week', 
  THIS_MONTH = 'this_month',
  LAST_MONTH = 'last_month',
  CUSTOM = 'custom'
}

// Date filter configuration
export const dateFilters = {
  [DateFilterOption.ALL_TIME]: { label: 'All Time' },
  [DateFilterOption.TODAY]: { label: 'Today' },
  [DateFilterOption.YESTERDAY]: { label: 'Yesterday' },
  [DateFilterOption.THIS_WEEK]: { label: 'This Week' },
  [DateFilterOption.LAST_WEEK]: { label: 'Last Week' },
  [DateFilterOption.THIS_MONTH]: { label: 'This Month' },
  [DateFilterOption.LAST_MONTH]: { label: 'Last Month' },
  [DateFilterOption.CUSTOM]: { label: 'Custom Range' },
};

export interface DateRangeParams {
  startDate: Date | undefined;
  endDate: Date | undefined;
}

interface DateFilterSelectorProps {
  currentFilter: DateFilterOption;
  customDateRange: DateRangeParams;
  onFilterChange: (filter: DateFilterOption, range?: DateRangeParams) => void;
}

const screenWidth = Dimensions.get("window").width;

const DateFilterSelector: React.FC<DateFilterSelectorProps> = ({
  currentFilter,
  customDateRange,
  onFilterChange
}) => {
  const theme = useTheme();
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [filterMenuVisible, setFilterMenuVisible] = useState(false);

  // Get label for current filter
  const getCurrentFilterLabel = () => {
    if (currentFilter === DateFilterOption.CUSTOM && customDateRange.startDate) {
      const startStr = customDateRange.startDate.toLocaleDateString();
      const endStr = customDateRange.endDate 
        ? customDateRange.endDate.toLocaleDateString() 
        : 'Present';
      return `${startStr} - ${endStr}`;
    }
    return dateFilters[currentFilter].label;
  };

  // Date picker handlers
  const onDismissDatePicker = () => {
    setDatePickerVisible(false);
  };

  const onConfirmDateRange = (params: DateRangeParams) => {
    setDatePickerVisible(false);
    onFilterChange(DateFilterOption.CUSTOM, params);
  };

  const styles = StyleSheet.create({
    filterContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      padding: 8,
      elevation: 2,
    },
    filterButton: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    filterLabel: {
      marginLeft: 4,
      color: theme.colors.primary,
      fontWeight: '500',
    },
  });

  return (
    <>
      <View style={styles.filterContainer}>
        <Text variant="bodyMedium">Period: {getCurrentFilterLabel()}</Text>
        <Button 
          mode="text" 
          onPress={() => setFilterMenuVisible(true)}
          icon={() => <MaterialCommunityIcons name="filter-variant" size={18} color={theme.colors.primary} />}
        >
          Change Filter
        </Button>
      </View>

      {/* Date Picker Modal & Filter Menu */}
      <DatePickerModal
        locale="en"
        mode="range"
        visible={datePickerVisible}
        onDismiss={onDismissDatePicker}
        startDate={customDateRange.startDate}
        endDate={customDateRange.endDate}
        onConfirm={onConfirmDateRange}
      />
      
      <Menu
        visible={filterMenuVisible}
        onDismiss={() => setFilterMenuVisible(false)}
        anchor={{x: screenWidth - 20, y: 64}}
        contentStyle={{ backgroundColor: theme.colors.elevation.level2 }}
      >
        {/* Common filter options */}
        <Menu.Item
          onPress={() => {
            onFilterChange(DateFilterOption.ALL_TIME);
            setFilterMenuVisible(false);
          }}
          title={dateFilters[DateFilterOption.ALL_TIME].label}
          leadingIcon="calendar-multiple"
        />
        <Menu.Item
          onPress={() => {
            onFilterChange(DateFilterOption.TODAY);
            setFilterMenuVisible(false);
          }}
          title={dateFilters[DateFilterOption.TODAY].label}
          leadingIcon="calendar-today"
        />
        <Menu.Item
          onPress={() => {
            onFilterChange(DateFilterOption.YESTERDAY);
            setFilterMenuVisible(false);
          }}
          title={dateFilters[DateFilterOption.YESTERDAY].label}
          leadingIcon="calendar-arrow-left"
        />
        <Divider />
        {/* Week & Month options */}
        <Menu.Item
          onPress={() => {
            onFilterChange(DateFilterOption.THIS_WEEK);
            setFilterMenuVisible(false);
          }}
          title={dateFilters[DateFilterOption.THIS_WEEK].label}
          leadingIcon="calendar-week"
        />
        <Menu.Item
          onPress={() => {
            onFilterChange(DateFilterOption.LAST_WEEK);
            setFilterMenuVisible(false);
          }}
          title={dateFilters[DateFilterOption.LAST_WEEK].label}
          leadingIcon="calendar-weekend"
        />
        <Menu.Item
          onPress={() => {
            onFilterChange(DateFilterOption.THIS_MONTH);
            setFilterMenuVisible(false);
          }}
          title={dateFilters[DateFilterOption.THIS_MONTH].label}
          leadingIcon="calendar-month"
        />
        <Menu.Item
          onPress={() => {
            onFilterChange(DateFilterOption.LAST_MONTH);
            setFilterMenuVisible(false);
          }}
          title={dateFilters[DateFilterOption.LAST_MONTH].label}
          leadingIcon="calendar-month-outline"
        />
        <Divider />
        {/* Custom range option */}
        <Menu.Item
          onPress={() => {
            setFilterMenuVisible(false);
            setDatePickerVisible(true);
          }}
          title={dateFilters[DateFilterOption.CUSTOM].label}
          leadingIcon="calendar-range"
        />
      </Menu>
    </>
  );
};

export default DateFilterSelector; 