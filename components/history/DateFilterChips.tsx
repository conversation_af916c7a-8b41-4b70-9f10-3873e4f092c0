import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Chip, Text, TouchableRipple, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { DateFilterPreset } from '@/utils/dateUtils';

interface DateFilterChipsProps {
  activeDatePreset: DateFilterPreset;
  onSelectDatePreset: (preset: DateFilterPreset) => void;
  dateRange: {
    startDate: Date | undefined;
    endDate: Date | undefined;
  };
  onClearDateFilter: () => void;
}

const DateFilterChips: React.FC<DateFilterChipsProps> = ({
  activeDatePreset,
  onSelectDatePreset,
  dateRange,
  onClearDateFilter
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  // Simplified filter options for cleaner UI
  const filterOptions = [
    { id: 'all', label: 'All', icon: 'calendar-multiple' },
    { id: 'today', label: 'Today', icon: 'calendar-today' },
    { id: 'yesterday', label: 'Yesterday', icon: 'calendar-arrow-left' },
    { id: 'this_week', label: 'This Week', icon: 'calendar-week' },
    { id: 'last_week', label: 'Last Week', icon: 'calendar-weekend' },
    { id: 'this_month', label: 'This Month', icon: 'calendar-month' },
    { id: 'last_month', label: 'Last Month', icon: 'calendar-month-outline' },
    { id: 'custom', label: 'Custom', icon: 'calendar-search' },
  ];

  return (
    <View style={styles.container}>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false} 
        contentContainerStyle={styles.scrollContent}
        style={styles.scrollView}
      >
        {filterOptions.map(option => (
          <Chip 
            key={option.id}
            selected={activeDatePreset === option.id}
            style={[
              styles.filterChip, 
              activeDatePreset === option.id ? styles.activeChip : styles.inactiveChip
            ]}
            onPress={() => onSelectDatePreset(option.id as DateFilterPreset)}
            icon={option.icon}
            mode="flat"
            compact
            showSelectedCheck={false}
            elevation={0}
          >
            {option.label}
          </Chip>
        ))}
      </ScrollView>
      
      {/* Custom date range display */}
      {activeDatePreset === 'custom' && (dateRange.startDate || dateRange.endDate) && (
        <View style={styles.dateFilterContainer}>
          <MaterialCommunityIcons 
            name="calendar-range" 
            size={18} 
            color={theme.colors.primary} 
            style={styles.calendarIcon}
          />
          <Text style={styles.dateFilterText}>
            {dateRange.startDate ? dateRange.startDate.toLocaleDateString() : 'Any'} 
            {' - '} 
            {dateRange.endDate ? dateRange.endDate.toLocaleDateString() : 'Any'}
          </Text>
          <TouchableRipple
            onPress={onClearDateFilter}
            style={styles.clearButton}
            borderless
            rippleColor={theme.colors.errorContainer}
          >
            <MaterialCommunityIcons name="close-circle" size={20} color={theme.colors.error} />
          </TouchableRipple>
        </View>
      )}
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    backgroundColor: 'transparent',
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingVertical: 8
  },
  filterChip: {
    marginRight: 8,
    height: 36,
    borderRadius: 18,
  },
  activeChip: {
    backgroundColor: theme.colors.primaryContainer,
  },
  inactiveChip: {
    backgroundColor: theme.colors.surfaceVariant,
  },
  dateFilterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginHorizontal: 16,
    marginBottom: 12,
  },
  calendarIcon: {
    marginRight: 8,
  },
  dateFilterText: {
    flex: 1,
    color: theme.colors.onSurface,
    fontSize: 14,
    fontWeight: '500',
  },
  clearButton: {
    padding: 4,
    borderRadius: 20
  }
});

export default DateFilterChips; 