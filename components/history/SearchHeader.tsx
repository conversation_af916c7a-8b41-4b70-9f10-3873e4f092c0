import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Appbar, Searchbar, Text, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface SearchHeaderProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

const SearchHeader: React.FC<SearchHeaderProps> = ({
  searchQuery,
  onSearchChange
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      <Appbar.Header style={styles.appbarHeader}>
        <View style={styles.appbarContent}>
          <View style={styles.titleContainer}>
            <MaterialCommunityIcons name="history" size={24} color={theme.colors.primary} />
            <Text style={styles.appbarTitle}>Bill History</Text>
          </View>
        </View>
      </Appbar.Header>
      
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search bills..."
          onChangeText={onSearchChange}
          value={searchQuery}
          style={styles.searchbar}
          iconColor={theme.colors.primary}
          clearIcon="close-circle"
          clearButtonMode="while-editing"
        />
      </View>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.outlineVariant,
    backgroundColor: theme.colors.background,
  },
  appbarHeader: {
    backgroundColor: 'transparent',
    elevation: 0,
    height: 60,
    justifyContent: 'center',
  },
  appbarContent: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  appbarTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: theme.colors.onSurface,
    marginLeft: 12,
    letterSpacing: 0.15,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  searchbar: {
    borderRadius: 20,
    height: 48,
    backgroundColor: theme.colors.surfaceVariant,
    elevation: 0,
  }
});

export default SearchHeader; 