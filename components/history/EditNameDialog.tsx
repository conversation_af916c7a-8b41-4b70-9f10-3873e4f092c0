import React from 'react';
import { StyleSheet, View } from 'react-native';
import { <PERSON>ton, Dialog, Divider, IconButton, Text, TextInput, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface EditNameDialogProps {
  visible: boolean;
  onDismiss: () => void;
  onSave: () => void;
  name: string;
  onChangeName: (value: string) => void;
}

const EditNameDialog: React.FC<EditNameDialogProps> = ({
  visible,
  onDismiss,
  onSave,
  name,
  onChangeName
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <Dialog visible={visible} onDismiss={onDismiss} style={styles.editDialog}>
      <View style={[styles.dialogHeader, { backgroundColor: theme.colors.tertiaryContainer }]}>
        <View style={[styles.dialogIconContainer, { backgroundColor: theme.colors.tertiary }]}>
          <MaterialCommunityIcons
            name="pencil"
            size={24}
            color={theme.colors.onTertiary}
          />
        </View>
        <View style={styles.dialogHeaderText}>
          <Text style={[styles.dialogTitle, { color: theme.colors.onTertiaryContainer }]}>
            Edit Bill Name
          </Text>
          <Text style={[styles.dialogSubtitle, { color: theme.colors.onTertiaryContainer }]}>
            Update customer information
          </Text>
        </View>
        <IconButton
          icon="close"
          size={20}
          onPress={onDismiss}
          style={styles.closeButton}
          iconColor={theme.colors.onTertiaryContainer}
        />
      </View>

      <Dialog.Content style={styles.dialogContent}>
        <TextInput
          label="Customer Name"
          value={name}
          onChangeText={onChangeName}
          style={styles.editDialogInput}
          mode="outlined"
          autoFocus
          theme={theme}
          placeholder="Enter customer name..."
        />
      </Dialog.Content>

      <Dialog.Actions style={styles.dialogActions}>
        <Button
          onPress={onDismiss}
          mode="outlined"
          style={styles.dialogButton}
          textColor={theme.colors.onSurface}
        >
          Cancel
        </Button>
        <Button
          onPress={onSave}
          mode="contained"
          buttonColor={theme.colors.primary}
          textColor={theme.colors.onPrimary}
          style={styles.dialogButton}
          disabled={name.trim() === ''}
        >
          Save
        </Button>
      </Dialog.Actions>
    </Dialog>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  editDialog: {
    backgroundColor: theme.colors.surface,
    borderRadius: 24,
    padding: 0,
    marginHorizontal: 20,
    marginVertical: 40,
    elevation: 8,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  dialogHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  dialogIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  dialogHeaderText: {
    flex: 1,
  },
  dialogTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 2,
  },
  dialogSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    opacity: 0.8,
  },
  closeButton: {
    margin: 0,
  },
  dialogContent: {
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  editDialogInput: {
    backgroundColor: 'transparent',
    fontSize: 16,
  },
  dialogActions: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
  },
  dialogButton: {
    minWidth: 80,
  },
});

export default EditNameDialog;