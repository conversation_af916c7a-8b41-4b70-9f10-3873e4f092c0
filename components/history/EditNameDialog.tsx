import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Button, Dialog, Divider, IconButton, Text, TextInput, useTheme } from 'react-native-paper';

interface EditNameDialogProps {
  visible: boolean;
  onDismiss: () => void;
  onSave: () => void;
  name: string;
  onChangeName: (value: string) => void;
}

const EditNameDialog: React.FC<EditNameDialogProps> = ({
  visible,
  onDismiss,
  onSave,
  name,
  onChangeName
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <Dialog visible={visible} onDismiss={onDismiss} style={styles.editDialog}>
      <View style={styles.dialogHeader}>
        <Text style={styles.editDialogTitle}>Edit Bill Name</Text>
        <IconButton
          icon="close"
          size={20}
          onPress={onDismiss}
          style={styles.closeButton}
        />
      </View>
      
      <Dialog.Content style={styles.dialogContent}>
        <TextInput
          label="Customer Name"
          value={name}
          onChangeText={onChangeName}
          style={styles.editDialogInput}
          mode="outlined"
          autoFocus
          outlineColor={theme.colors.outline}
          activeOutlineColor={theme.colors.primary}
        />
      </Dialog.Content>
      
      <Divider />
      
      <Dialog.Actions style={styles.dialogActions}>
        <Button 
          onPress={onDismiss}
          textColor={theme.colors.onSurfaceVariant}
          style={styles.cancelButton}
        >
          Cancel
        </Button>
        <Button 
          onPress={onSave} 
          mode="contained"
          buttonColor={theme.colors.primary}
          style={styles.saveButton}
        >
          Save
        </Button>
      </Dialog.Actions>
    </Dialog>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  editDialog: {
    backgroundColor: theme.colors.background,
    borderRadius: 28,
    padding: 0,
  },
  dialogHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 24,
    paddingRight: 8,
    paddingVertical: 16,
  },
  editDialogTitle: {
    color: theme.colors.onSurface,
    fontWeight: '700',
    fontSize: 20,
  },
  closeButton: {
    margin: 0,
  },
  dialogContent: {
    paddingTop: 0,
    paddingBottom: 24,
  },
  editDialogInput: {
    backgroundColor: 'transparent',
    fontSize: 16,
  },
  dialogActions: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  cancelButton: {
    borderRadius: 20,
  },
  saveButton: {
    borderRadius: 20,
  }
});

export default EditNameDialog; 