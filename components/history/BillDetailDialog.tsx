import React, { useEffect, useState } from 'react';
import { Sc<PERSON>View, StyleSheet, View } from 'react-native';
import { Dialog, Divider, IconButton, Surface, Text, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Bill } from '@/types/bill';
import { getCurrencySymbol, DEFAULT_CURRENCY_SYMBOL } from '@/utils/billUtils';

// Simple props type
type BillDetailDialogProps = {
  visible: boolean;
  bill: Bill | null;
  onDismiss: () => void;
  onShare: (bill: Bill) => void;
  onPrint: (bill: Bill) => void;
  onEdit: (bill: Bill) => void;
  onDelete: (billId: string) => void;
};

// Simplified component without destructuring
export default function BillDetailDialog(props: BillDetailDialogProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [currSymbol, setCurrSymbol] = useState(DEFAULT_CURRENCY_SYMBOL);

  // Don't render anything if bill is null
  if (!props.bill) return null;

  // Load currency symbol when dialog becomes visible
  useEffect(() => {
    if (props.visible && props.bill) {
      getCurrencySymbol().then(symbol => {
        setCurrSymbol(symbol);
      });
    }
  }, [props.visible, props.bill]);

  // Helper functions
  function formatCurrency(amount: number) {
    return `${currSymbol}${amount.toFixed(2)}`;
  }
  
  function formatDate(dateString: string) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  }

  // Render the dialog
  return (
    <Dialog visible={props.visible} onDismiss={props.onDismiss} style={styles.detailDialog}>
      <View style={styles.dialogHeader}>
        <Text style={styles.dialogTitle}>Bill Details</Text>
        <IconButton
          icon="close"
          size={20}
          onPress={props.onDismiss}
          style={styles.closeButton}
        />
      </View>
      
      <Dialog.ScrollArea style={styles.scrollArea}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Header with bill name and amount */}
          <View style={styles.detailHeader}>
            <Surface style={styles.billHeaderSurface} elevation={0}>
              <View style={styles.detailHeaderContent}>
                <View style={styles.detailHeaderInfo}>
                  <Text style={styles.detailName}>
                    {props.bill.name || `Bill #${props.bill.id.slice(-8)}`}
                  </Text>
                  <View style={styles.detailDate}>
                    <MaterialCommunityIcons 
                      name="calendar" 
                      size={16} 
                      color={theme.colors.primary} 
                      style={{ marginRight: 6 }}
                    />
                    <Text style={styles.dateText}>{formatDate(props.bill.date)}</Text>
                  </View>
                </View>
                <Text style={styles.totalAmountDisplay}>
                  {formatCurrency(props.bill.finalAmount ?? props.bill.totalAmount)}
                </Text>
              </View>
            </Surface>
          </View>

          {/* Items Header */}
          <Text style={styles.sectionTitle}>Items</Text>

          {/* Bill Items Table */}
          <Surface style={styles.detailTable} elevation={0}>
            <View style={styles.detailTableHeader}>
              <Text style={[styles.headerCell, { flex: 2.5 }]}>Item</Text>
              <Text style={[styles.headerCell, { flex: 1, textAlign: 'center' }]}>Qty</Text>
              <Text style={[styles.headerCell, { flex: 1.5, textAlign: 'right' }]}>Price</Text>
              <Text style={[styles.headerCell, { flex: 1.5, textAlign: 'right' }]}>Amount</Text>
            </View>

            {props.bill.items.map((item, index) => (
              <View key={index} style={styles.detailTableRow}>
                <Text style={styles.itemNameCellDialog} numberOfLines={2}>
                  {item.name || `Item ${index + 1}`}
                </Text>
                <Text style={styles.quantityCellDialog}>
                  {item.quantity}
                </Text>
                <Text style={styles.priceCellDialog}>
                  {formatCurrency(item.price)}
                </Text>
                <Text style={styles.amountCellDialog}>
                  {formatCurrency(item.amount)}
                </Text>
              </View>
            ))}
          </Surface>

          {/* Bill Totals */}
          <Text style={styles.sectionTitle}>Summary</Text>
          
          <Surface style={styles.totalsContainer} elevation={0}>
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Subtotal</Text>
              <Text style={styles.totalAmount}>
                {formatCurrency(props.bill.totalAmount)}
              </Text>
            </View>

            {!!(props.bill.discountAmount && props.bill.discountAmount > 0) && (
              <View style={styles.totalRow}>
                <Text style={styles.totalLabel}>Discount</Text>
                <Text style={styles.discountAmount}>
                  {`- ${formatCurrency(props.bill.discountAmount)}`}
                </Text>
              </View>
            )}

            {!!(props.bill.finalAmount && props.bill.finalAmount !== props.bill.totalAmount) && (
              <>
                <Divider style={styles.totalsDivider} />
                <View style={styles.finalTotalRow}>
                  <Text style={styles.finalTotalLabel}>Total</Text>
                  <Text style={styles.finalTotalAmount}>
                    {formatCurrency(props.bill.finalAmount)}
                  </Text>
                </View>
              </>
            )}
          </Surface>
        </ScrollView>
      </Dialog.ScrollArea>

      <Divider />

      <Dialog.Actions style={styles.actionButtons}>
        <IconButton
          icon="pencil-outline"
          mode="contained-tonal"
          size={20}
          onPress={() => props.onEdit(props.bill!)}
          style={styles.actionIcon}
          containerColor={theme.colors.surfaceVariant}
        />
        <IconButton
          icon="printer-outline"
          mode="contained-tonal"
          size={20}
          onPress={() => props.onPrint(props.bill!)}
          style={styles.actionIcon}
          containerColor={theme.colors.surfaceVariant}
        />
        <IconButton
          icon="share-variant-outline"
          mode="contained-tonal"
          size={20}
          onPress={() => props.onShare(props.bill!)}
          style={styles.actionIcon}
          containerColor={theme.colors.surfaceVariant}
        />
        <IconButton
          icon="trash-can-outline"
          mode="contained-tonal"
          size={20}
          onPress={() => props.onDelete(props.bill!.id)}
          style={styles.actionIcon}
          iconColor={theme.colors.error}
          containerColor={theme.colors.errorContainer}
        />
      </Dialog.Actions>
    </Dialog>
  );
}

// Styles definition
const createStyles = (theme: any) => StyleSheet.create({
  detailDialog: {
    backgroundColor: theme.colors.background,
    borderRadius: 28,
    padding: 0,
    marginHorizontal: 12,
    marginVertical: 20,
    maxHeight: '90%',
  },
  dialogHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 24,
    paddingRight: 8,
    paddingVertical: 16,
  },
  dialogTitle: {
    color: theme.colors.onSurface,
    fontWeight: '700',
    fontSize: 20,
  },
  closeButton: {
    margin: 0,
  },
  scrollArea: {
    paddingHorizontal: 0,
  },
  scrollView: {
    maxHeight: 350,
  },
  detailHeader: {
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  billHeaderSurface: {
    padding: 16,
    borderRadius: 16,
    backgroundColor: theme.colors.surface,
  },
  detailHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  detailHeaderInfo: {
    flex: 1,
  },
  detailName: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.onSurface,
    marginBottom: 6,
  },
  detailDate: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  totalAmountDisplay: {
    fontSize: 22,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.onSurfaceVariant,
    marginBottom: 8,
    paddingHorizontal: 16,
  },
  detailTable: {
    marginBottom: 24,
    marginHorizontal: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  detailTableHeader: {
    flexDirection: 'row',
    paddingVertical: 12,
    backgroundColor: theme.colors.primaryContainer,
    paddingHorizontal: 16,
  },
  detailTableRow: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.outlineVariant, 
  },
  headerCell: {
    fontWeight: 'bold',
    color: theme.colors.onPrimaryContainer,
    fontSize: 14,
  },
  itemNameCellDialog: { 
    flex: 2.5, 
    color: theme.colors.onSurface, 
    fontSize: 14 
  },
  quantityCellDialog: { 
    flex: 1, 
    textAlign: 'center', 
    color: theme.colors.onSurface, 
    fontSize: 14 
  },
  priceCellDialog: { 
    flex: 1.5, 
    textAlign: 'right', 
    color: theme.colors.onSurface, 
    fontSize: 14 
  },
  amountCellDialog: { 
    flex: 1.5, 
    textAlign: 'right', 
    color: theme.colors.onSurface, 
    fontSize: 14 
  },
  totalsContainer: {
    marginHorizontal: 16,
    marginBottom: 24,
    padding: 16,
    borderRadius: 16,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  totalLabel: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  totalAmount: {
    fontSize: 14,
    color: theme.colors.onSurface,
    fontWeight: '500',
  },
  discountAmount: {
    fontSize: 14,
    color: theme.colors.error,
    fontWeight: '500',
  },
  totalsDivider: {
    marginVertical: 8,
  },
  finalTotalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  finalTotalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  finalTotalAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  actionButtons: {
    justifyContent: 'space-around',
    paddingVertical: 12,
    flexDirection: 'row',
  },
  actionIcon: {
    margin: 0,
  }
}); 