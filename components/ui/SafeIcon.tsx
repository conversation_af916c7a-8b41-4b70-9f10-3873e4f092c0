import React from 'react';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import getValidIconName from '@/utils/iconUtils';

interface SafeIconProps {
  name: string;
  size: number;
  color: string;
  style?: any;
}

/**
 * A wrapper for MaterialCommunityIcons that checks if the icon name is valid
 * and uses a fallback if it's not.
 */
export function SafeIcon({ name, size, color, style }: SafeIconProps) {
  // Use our utility function to get a valid icon name and cast it to the required type
  const iconName = getValidIconName(name) as keyof typeof MaterialCommunityIcons.glyphMap;
  
  return (
    <MaterialCommunityIcons 
      name={iconName} 
      size={size} 
      color={color} 
      style={style} 
    />
  );
}

export default SafeIcon; 