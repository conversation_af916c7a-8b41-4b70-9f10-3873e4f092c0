import React, { useCallback, memo, useState, useMemo } from 'react';
import {
  FlatList,
  SectionList,
  View,
  StyleSheet,
  ActivityIndicator,
  Text,
  Platform
} from 'react-native';
import { useIsScrolling } from '@/utils/componentOptimization';

/**
 * OptimizedList - High performance list component with optimizations for React Native
 * 
 * Features:
 * - Virtualized rendering for better performance
 * - Optimized rendering during scrolling
 * - Configurable batching and window size
 * - Support for section lists
 * - Built-in loading indicators
 * - Support for pagination
 * 
 * @param {Object} props
 * @param {Array} props.data - Data array for the list
 * @param {Function} props.renderItem - Render function for each item
 * @param {boolean} props.isSectionList - Whether to render as a section list
 * @param {boolean} props.isLoading - Whether data is loading
 * @param {boolean} props.hasMore - Whether there are more items to load
 * @param {Function} props.onEndReached - Function to call when end is reached
 * @param {number} props.batchSize - Number of items to render in each batch
 * @param {number} props.windowSize - Window size for the FlatList
 * @param {Object} props.styles - Custom styles to apply
 */
const OptimizedList = memo(({
  data,
  renderItem,
  keyExtractor,
  isSectionList = false,
  isLoading = false,
  isEmpty = false,
  hasMore = false,
  onEndReached,
  initialNumToRender = 10,
  maxToRenderPerBatch = 10,
  windowSize = 5,
  emptyComponent,
  ListHeaderComponent,
  ListFooterComponent,
  contentContainerStyle,
  style,
  onScroll,
  ...rest
}) => {
  // Track if list is scrolling to optimize rendering
  const { isScrolling, handleScrollBegin, handleScrollEnd } = useIsScrolling();
  
  // Memoize key extractor to prevent re-renders
  const getItemKey = useCallback(keyExtractor || ((item, index) => {
    if (item.id) return `item-${item.id}`;
    return `item-${index}`;
  }), [keyExtractor]);
  
  // Memoize the getItemLayout function for fixed height items
  const getItemLayout = useCallback(rest.getItemLayout || ((data, index) => {
    // Default item height - should be customized for your specific use case
    const itemHeight = rest.itemHeight || 100;
    return {
      length: itemHeight,
      offset: itemHeight * index,
      index,
    };
  }), [rest.getItemLayout, rest.itemHeight]);
  
  // Combined scroll handler
  const handleScroll = useCallback((event) => {
    // Call the original onScroll handler if provided
    if (onScroll) {
      onScroll(event);
    }
    
    // Track scrolling state to optimize rendering
    const { velocity } = event.nativeEvent;
    if (velocity && (Math.abs(velocity.x) > 0 || Math.abs(velocity.y) > 0)) {
      handleScrollBegin();
    } else {
      handleScrollEnd();
    }
  }, [onScroll, handleScrollBegin, handleScrollEnd]);
  
  // Custom loading footer component
  const renderFooter = useCallback(() => {
    if (!hasMore && !isLoading) return null;
    
    return (
      <View style={styles.footer}>
        <ActivityIndicator size="small" color="#0000ff" />
        <Text style={styles.footerText}>Loading more...</Text>
      </View>
    );
  }, [hasMore, isLoading]);
  
  // Combine custom footer with loading indicator
  const combinedFooter = useMemo(() => {
    if (!ListFooterComponent && !hasMore && !isLoading) return null;
    
    return (
      <View>
        {ListFooterComponent}
        {hasMore && renderFooter()}
      </View>
    );
  }, [ListFooterComponent, hasMore, isLoading, renderFooter]);
  
  // Empty state
  const EmptyComponent = useMemo(() => {
    if (isLoading) return null;
    
    if (emptyComponent) {
      return emptyComponent;
    }
    
    if (isEmpty || (data && data.length === 0)) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No items found</Text>
        </View>
      );
    }
    
    return null;
  }, [isLoading, isEmpty, data, emptyComponent]);
  
  // Loading state
  if (isLoading && (!data || data.length === 0)) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }
  
  // Choose between FlatList and SectionList based on props
  const ListComponent = isSectionList ? SectionList : FlatList;
  
  return (
    <ListComponent
      data={data}
      sections={isSectionList ? data : undefined}
      renderItem={renderItem}
      keyExtractor={getItemKey}
      getItemLayout={Platform.OS !== 'web' ? getItemLayout : undefined} // getItemLayout can cause issues on web
      initialNumToRender={initialNumToRender}
      maxToRenderPerBatch={maxToRenderPerBatch}
      windowSize={windowSize}
      removeClippedSubviews={Platform.OS !== 'web'} // Can cause issues on web
      onEndReached={onEndReached}
      onEndReachedThreshold={0.5}
      onScroll={handleScroll}
      scrollEventThrottle={16}
      ListHeaderComponent={ListHeaderComponent}
      ListFooterComponent={combinedFooter}
      ListEmptyComponent={EmptyComponent}
      contentContainerStyle={[
        styles.contentContainer,
        isEmpty && styles.emptyContentContainer,
        contentContainerStyle
      ]}
      style={[styles.list, style]}
      // Performance optimizations
      updateCellsBatchingPeriod={50}
      {...rest}
    />
  );
});

const styles = StyleSheet.create({
  list: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
  },
  emptyContentContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footer: {
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  footerText: {
    marginLeft: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
  },
});

export default OptimizedList; 