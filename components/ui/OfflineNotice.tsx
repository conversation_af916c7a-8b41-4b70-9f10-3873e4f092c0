import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Text, useTheme } from 'react-native-paper';

export function OfflineNotice() {
  const theme = useTheme();
  const { isConnected } = useNetworkStatus();
  
  if (isConnected) {
    return null;
  }
  
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.errorContainer }]}>
      <MaterialCommunityIcons 
        name="wifi-off" 
        size={16} 
        color={theme.colors.onErrorContainer} 
        style={styles.icon}
      />
      <Text style={[styles.text, { color: theme.colors.onErrorContainer }]}>
        You are offline. The app will continue to work.
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 36,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    zIndex: 1000,
  },
  icon: {
    marginRight: 8,
  },
  text: {
    fontSize: 14,
    fontWeight: '500',
  },
}); 