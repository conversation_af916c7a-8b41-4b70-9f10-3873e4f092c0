
// This is a shim for web and Android where the tab bar is generally opaque.
// Provide a simple null-rendering component instead of raw null/undefined.
const TabBarBackgroundComponent = () => {
  return null; // Or <View style={{ flex: 1, backgroundColor: 'transparent' }} /> if a view is strictly needed
};
export default TabBarBackgroundComponent;

export function useBottomTabOverflow() {
  return 0;
}
