import React, { useState, useEffect, memo } from 'react';
import { StyleSheet, View, ActivityIndicator } from 'react-native';
import { Image } from 'expo-image';
import { optimizeImage } from '@/utils/imageOptimization';

const blurhash =
  '|rF?hV%2WCj[ayj[a|j[az_NaeWBj@ayfRayfQfQM{M|azj[azf6fQfQfQIpWXofj[ayj[j[fQayWCoeoeaya}j[ayfQa{oLj?j[WVj[ayayj[fQoff7azayj[ayj[j[ayofayayayj[fQj[ayayj[ayfjj[j[ayjuayj[';

/**
 * OptimizedImage - A high-performance image component with optimization
 * 
 * @param {Object} props
 * @param {string} props.source - Image URI or require() source
 * @param {string} props.size - Size preset ('thumbnail', 'profile', 'medium', 'large')
 * @param {number} props.quality - JPEG quality (0.0 to 1.0)
 * @param {Object} props.style - Additional styles
 * @param {string} props.contentFit - Content fit ('cover', 'contain', 'fill', 'none', 'scale-down')
 * @param {boolean} props.showLoader - Whether to show a loader while optimizing
 */
const OptimizedImage = memo(({
  source,
  size = 'medium',
  quality = 0.8,
  style,
  contentFit = 'cover',
  showLoader = true,
  ...props
}) => {
  const [optimizedSource, setOptimizedSource] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    let isMounted = true;
    
    const prepareImage = async () => {
      try {
        setIsLoading(true);
        
        // Handle different source types
        let uri;
        if (typeof source === 'string') {
          uri = source;
        } else if (source && source.uri) {
          uri = source.uri;
        } else if (typeof source === 'number') {
          // For require('./image.jpg')
          setOptimizedSource(source);
          setIsLoading(false);
          return;
        } else {
          setOptimizedSource(null);
          setIsLoading(false);
          return;
        }
        
        // Optimize the image
        const optimizedUri = await optimizeImage(uri, size, quality);
        
        if (isMounted) {
          setOptimizedSource({ uri: optimizedUri });
          setIsLoading(false);
        }
      } catch (error) {
        console.error('Error preparing image:', error);
        if (isMounted) {
          // Fallback to original source on error
          setOptimizedSource(typeof source === 'string' ? { uri: source } : source);
          setIsLoading(false);
        }
      }
    };
    
    prepareImage();
    
    return () => {
      isMounted = false;
    };
  }, [source, size, quality]);
  
  // If no source, render nothing
  if (!source) {
    return null;
  }
  
  return (
    <View style={[styles.container, style]}>
      {(isLoading && showLoader) && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="small" color="#0000ff" />
        </View>
      )}
      
      <Image
        source={optimizedSource || (typeof source === 'string' ? { uri: source } : source)}
        style={[styles.image, style]}
        contentFit={contentFit}
        transition={300}
        placeholder={blurhash}
        cachePolicy="memory-disk"
        recyclingKey={`${size}-${quality}-${typeof source === 'string' ? source : (source?.uri || '')}`}
        {...props}
      />
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  loaderContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(240, 240, 240, 0.7)',
  },
});

export default OptimizedImage; 