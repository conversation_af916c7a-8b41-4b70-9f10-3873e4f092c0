import React from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Text, Surface, useTheme, IconButton } from 'react-native-paper';
import { BillItem } from '@/types/bill';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface BillTableProps {
  items: BillItem[];
  currencySymbol?: string;
  showItemNameColumn?: boolean;
  subTotal: number;
  discountAmount: number;
  finalAmount: number;
  onRemoveItem: (index: number) => void;
}

const BillTable: React.FC<BillTableProps> = ({
  items,
  currencySymbol = '₹',
  showItemNameColumn = false,
  subTotal,
  discountAmount,
  finalAmount,
  onRemoveItem,
}) => {
  const theme = useTheme();

  const validItems = items.filter(item => item.quantity > 0 && item.price > 0);

  const srCellWidth = showItemNameColumn ? '8%' : '10%';
  const detailsCellWidth = '27%';
  const qtyCellWidth = showItemNameColumn ? '13%' : '20%';
  const rateCellWidth = showItemNameColumn ? '17%' : '25%';
  const totalCellWidth = showItemNameColumn ? '20%' : '30%';
  const actionCellWidth = '15%';

  return (
    <View style={styles.container}>
      {/* Table header */}
      <View style={[styles.tableHeader, { backgroundColor: theme.colors.primary }]}>
        <Text style={[styles.headerCell, { width: srCellWidth }]}>SR</Text>
        {showItemNameColumn && <Text style={[styles.headerCell, { width: detailsCellWidth }]}>DETAILS</Text>}
        <Text style={[styles.headerCell, { width: qtyCellWidth }]}>QTY</Text>
        <Text style={[styles.headerCell, { width: rateCellWidth }]}>RATE</Text>
        <Text style={[styles.headerCell, { width: totalCellWidth }]}>TOTAL</Text>
        <Text style={[styles.headerCell, { width: actionCellWidth }]}> </Text>
      </View>
      
      {/* Table body - scrollable */}
      <ScrollView style={styles.tableBody}>
        {validItems.length > 0 ? (
          validItems.map((item, index) => (
            <View key={index} style={styles.tableRow}>
              <Text style={[styles.bodyCell, { width: srCellWidth }]}>{index + 1}</Text>
              {showItemNameColumn && (
                <Text style={[styles.bodyCell, { width: detailsCellWidth }]} numberOfLines={1} ellipsizeMode="tail">
                  {item.name || 'Item'}
                </Text>
              )}
              <Text style={[styles.bodyCell, { width: qtyCellWidth }]}>{item.quantity}</Text>
              <Text style={[styles.bodyCell, { width: rateCellWidth }]}>{item.price.toFixed(2)}</Text>
              <Text style={[styles.bodyCell, { width: totalCellWidth }]}>{item.amount.toFixed(2)}</Text>
              <View style={[styles.actionCell, { width: actionCellWidth }]}>
                <IconButton
                  icon="trash-can-outline"
                  iconColor={theme.colors.error}
                  size={20}
                  onPress={() => onRemoveItem(index)}
                  style={styles.removeButton}
                />
              </View>
            </View>
          ))
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No items added yet.</Text>
          </View>
        )}
      </ScrollView>

      {/* Totals section */}
      <View style={styles.footerContainer}>
        <View style={styles.footerRow}>
          <Text style={styles.footerLabel}>Subtotal:</Text>
          <Text style={styles.footerValue}>{currencySymbol}{subTotal.toFixed(2)}</Text>
        </View>
        {discountAmount > 0 && (
          <View style={styles.footerRow}>
            <Text style={styles.footerLabel}>Discount:</Text>
            <Text style={[styles.footerValue, {color: theme.colors.error}]}>-{currencySymbol}{discountAmount.toFixed(2)}</Text>
          </View>
        )}
        <View style={[styles.footerRow, styles.grandTotalRow]}>
          <Text style={[styles.footerLabel, styles.grandTotalLabel]}>Grand Total:</Text>
          <Text style={[styles.footerValue, styles.grandTotalValue]}>{currencySymbol}{finalAmount.toFixed(2)}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#212121', // Dark background for the table
  },
  tableHeader: {
    flexDirection: 'row',
    paddingVertical: 8, // Slightly reduced padding
  },
  tableBody: {
    flex: 1,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
    paddingVertical: 6, // Reduced padding to make rows thinner
    alignItems: 'center', // Ensure vertical alignment for all cell content
  },
  headerCell: {
    color: '#ffffff',
    fontWeight: 'bold',
    fontSize: 15, // Slightly smaller font for header
    textAlign: 'center',
  },
  bodyCell: {
    color: '#ffffff',
    fontSize: 14,
    textAlign: 'center',
    paddingVertical: 4, // Added small padding for cell content alignment
  },
  srCell: {
    // width: '10%', // Width is set dynamically
  },
  detailsCell: {
    width: '30%',
  },
  qtyCell: {
    width: '15%',
  },
  rateCell: {
    width: '20%',
  },
  totalCell: {
    width: '25%',
  },
  qtyCellNoDetails: {
    width: '25%',
  },
  rateCellNoDetails: {
    width: '30%',
  },
  totalCellNoDetails: {
    width: '35%',
  },
  actionCell: {
    justifyContent: 'center',
    alignItems: 'center',
    // paddingVertical: 0, // Ensure no extra padding here if bodyCells have it
  },
  removeButton: {
    margin: 0,
    padding: 0,
    height: 28, // Explicit height to help alignment
    width: 28,  // Explicit width
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    color: '#888888',
    fontSize: 16,
  },
  footerContainer: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    backgroundColor: '#333333', // Slightly lighter than table body for separation
    borderTopWidth: 1,
    borderTopColor: '#444444',
  },
  footerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 5,
  },
  footerLabel: {
    color: '#cccccc',
    fontSize: 16,
  },
  footerValue: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  grandTotalRow: {
    borderTopWidth: 1,
    borderTopColor: '#555555',
    marginTop: 5,
    paddingTop: 10,
  },
  grandTotalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  grandTotalValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
});

export default BillTable; 