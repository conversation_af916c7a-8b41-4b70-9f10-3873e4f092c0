import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Surface, Text, TextInput, Divider, useTheme, type MD3Theme as ReactNativePaperTheme } from 'react-native-paper';
// import Animated, { Layout } from 'react-native-reanimated'; // Comment out if Animated or Layout not used
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface BillSummaryProps {
  totalAmount: number;
  discountAmount: number;
  finalTotal: number;
  discountValueInput: string;
  onDiscountChange: (value: string) => void;
  currencySymbol?: string;
  isReviewMode?: boolean;
}

// const AnimatedSurface = Animated.createAnimatedComponent(Surface); // Comment out or remove

const BillSummary: React.FC<BillSummaryProps> = ({
  totalAmount,
  discountAmount,
  finalTotal,
  discountValueInput,
  onDiscountChange,
  currencySymbol = '₹',
  isReviewMode = false,
}) => {
  const theme = useTheme();
  const dynamicStyles = styles(theme);

  return (
    <View style={dynamicStyles.container}>
      <View style={dynamicStyles.sectionHeader}>
        <View style={dynamicStyles.sectionTitleContainer}>
          <MaterialCommunityIcons 
            name="calculator-variant-outline" 
            size={24} 
            color={theme.colors.primary} 
            style={dynamicStyles.sectionIcon} 
          />
          <Text style={dynamicStyles.sectionTitle}>Summary</Text>
        </View>
      </View>

      <Surface style={dynamicStyles.surface} elevation={0} /* layout={Layout.springify()} Removed layout animation */>
        <View style={dynamicStyles.contentContainer}>
          <View style={dynamicStyles.summaryRow}>
            <Text style={dynamicStyles.summaryLabel}>Subtotal</Text>
            <Text style={dynamicStyles.summaryValue}>{`${currencySymbol}${totalAmount.toFixed(2)}`}</Text>
          </View>

          <View style={dynamicStyles.discountRow}>
            <Text style={dynamicStyles.summaryLabel}>Discount</Text>
            <View style={dynamicStyles.discountInputContainer}>
              <TextInput
                value={discountValueInput}
                onChangeText={onDiscountChange}
                placeholder="0.00"
                keyboardType="numeric"
                mode="outlined"
                outlineColor={theme.colors.outline}
                activeOutlineColor={theme.colors.primary}
                style={dynamicStyles.discountInput}
                left={<TextInput.Affix text={currencySymbol} />}
                editable={!isReviewMode}
                disabled={isReviewMode}
                placeholderTextColor={theme.colors.onSurfaceDisabled}
              />
            </View>
          </View>
          
          {discountAmount > 0 && (
            <View style={dynamicStyles.discountAppliedRow}>
              <Text style={dynamicStyles.appliedDiscountText}>
                (-{`${currencySymbol}${discountAmount.toFixed(2)}`})
              </Text>
            </View>
          )}

          <Divider style={dynamicStyles.divider} />

          <View style={dynamicStyles.totalRow}>
            <Text style={dynamicStyles.totalLabel}>TOTAL</Text>
            <Text style={dynamicStyles.totalValue}>{`${currencySymbol}${finalTotal.toFixed(2)}`}</Text>
          </View>
        </View>
      </Surface>
    </View>
  );
};

const styles = (theme: ReactNativePaperTheme) => StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  sectionHeader: {
    marginBottom: 16,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.onSurface,
  },
  sectionIcon: {
    marginRight: 8,
  },
  surface: {
    borderRadius: 16,
    backgroundColor: theme.colors.surface,
    overflow: 'hidden',
  },
  contentContainer: {
    padding: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 4,
  },
  discountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  discountAppliedRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 6,
    marginBottom: 12,
  },
  discountInputContainer: {
    width: '50%',
  },
  summaryLabel: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.onSurface,
  },
  discountInput: {
    backgroundColor: 'transparent',
  },
  appliedDiscountText: {
    fontSize: 14,
    fontStyle: 'italic',
    color: theme.colors.error,
  },
  divider: {
    marginVertical: 16,
    backgroundColor: theme.colors.outlineVariant,
    height: 1,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  totalValue: {
    fontSize: 22,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
});

export default BillSummary; 