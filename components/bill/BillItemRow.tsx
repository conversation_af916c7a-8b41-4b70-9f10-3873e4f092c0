import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { TextInput, IconButton, Text, Surface, useTheme, type MD3Theme as ReactNativePaperTheme } from 'react-native-paper';
import { BillItem } from '@/types/bill';
// import Animated, { FadeInDown, withTiming, useAnimatedStyle, useSharedValue } from 'react-native-reanimated'; // Comment out animated imports if no longer needed

interface BillItemRowProps {
  item: BillItem;
  index: number;
  showItemNameInput: boolean;
  onUpdateItem: (index: number, field: keyof BillItem, value: string | number) => void;
  onRemoveItem: (index: number) => void;
  currencySymbol?: string;
}

// const AnimatedView = Animated.createAnimatedComponent(View); // Comment out or remove

const BillItemRow: React.FC<BillItemRowProps> = ({
  item,
  index,
  showItemNameInput,
  onUpdateItem,
  onRemoveItem,
  currencySymbol = '₹',
}) => {
  const theme = useTheme();
  const dynamicStyles = styles(theme);
  
  // Animation values for proper reduced motion support - REMOVED
  // const opacity = useSharedValue(0); 
  // const translateY = useSharedValue(10);
  
  // Setup animation with proper timing and respect for reduced motion - REMOVED
  // useEffect(() => {
  //   const timeout = setTimeout(() => {
  //     opacity.value = withTiming(1, { duration: 300 });
  //     translateY.value = withTiming(0, { duration: 300 });
  //   }, index * 50);
  //   return () => clearTimeout(timeout);
  // }, [index, opacity, translateY]);
  
  // Create animated style that respects reduced motion settings - REMOVED
  // const animatedStyle = useAnimatedStyle(() => {
  //   return {
  //     opacity: opacity.value,
  //     transform: [{ translateY: translateY.value }]
  //   };
  // });
  
  // Local quantity state for smoother slider experience
  const [localQuantity, setLocalQuantity] = useState(item.quantity);
  
  // Update local quantity when item quantity changes from outside
  useEffect(() => {
    setLocalQuantity(item.quantity);
  }, [item.quantity]);
  
  // Handle quantity change from TextInput
  const handleQuantityInputChange = (text: string) => {
    const num = parseInt(text, 10);
    if (isNaN(num) || num < 1) {
      // Optionally, handle invalid input, e.g., set to 1 or show error
      // For now, let's allow empty input for typing, but ensure onUpdateItem gets a valid number
      setLocalQuantity(1); // Or keep current localQuantity if text is empty and valid before
      if (text === '') return; // Don't update upstream if empty, wait for blur or valid number
      onUpdateItem(index, 'quantity', 1);
    } else {
      setLocalQuantity(num);
      onUpdateItem(index, 'quantity', num);
    }
  };

  // Handle blur event for quantity TextInput
  const handleQuantityInputBlur = () => {
    if (localQuantity < 1 || isNaN(localQuantity)) {
      setLocalQuantity(1);
      onUpdateItem(index, 'quantity', 1);
    } else {
      // Ensure upstream is updated with the final validated number
      onUpdateItem(index, 'quantity', localQuantity);
    }
  };
  
  // Quick adjustments for quantity
  const incrementQuantity = () => {
    const newQuantity = localQuantity + 1;
    setLocalQuantity(newQuantity);
    onUpdateItem(index, 'quantity', newQuantity);
  };
  
  const decrementQuantity = () => {
    if (localQuantity > 1) { // Prevent going below 1
      const newQuantity = localQuantity - 1;
      setLocalQuantity(newQuantity);
      onUpdateItem(index, 'quantity', newQuantity);
    }
  };

  const handlePriceChange = (text: string) => {
    if (text === '') {
      onUpdateItem(index, 'price', 0); // Store 0 in state for empty input
    } else {
      onUpdateItem(index, 'price', text); // Pass the raw text; parent will parse
    }
  };

  return (
    <View // Changed from AnimatedView
      style={[dynamicStyles.itemContainer/*, animatedStyle*/]} // Removed animatedStyle
    >
      <Surface style={dynamicStyles.itemSurface} elevation={0}>
        <View style={dynamicStyles.itemHeader}>
          <Text style={dynamicStyles.itemIndexText}>Item {index + 1}</Text>
          <IconButton
            icon="trash-can-outline"
            size={20}
            onPress={() => onRemoveItem(index)}
            iconColor={theme.colors.error}
            style={dynamicStyles.deleteButton}
            mode="outlined"
            containerColor={theme.colors.errorContainer}
          />
        </View>
        
        <View style={dynamicStyles.itemDetails}>
          {showItemNameInput && (
            <TextInput
              label="Item Name"
              value={item.name}
              onChangeText={(text) => onUpdateItem(index, 'name', text)}
              style={[dynamicStyles.input, dynamicStyles.itemNameInput]}
              mode="outlined"
              outlineColor={theme.colors.outline}
              activeOutlineColor={theme.colors.primary}
            />
          )}
          
          <View style={dynamicStyles.row}>
            {/* Quantity Selector with TextInput */}
            <View style={dynamicStyles.quantityContainer}>
              <Text style={dynamicStyles.fieldLabel}>Quantity</Text>
              <View style={dynamicStyles.quantityControls}>
                <IconButton
                  icon="minus"
                  size={20} // Slightly larger for easier tap
                  onPress={decrementQuantity}
                  mode="outlined"
                  disabled={localQuantity <= 1}
                  style={dynamicStyles.quantityButton}
                  iconColor={theme.colors.onSurface}
                />
                <TextInput
                  value={String(localQuantity)}
                  onChangeText={handleQuantityInputChange}
                  onBlur={handleQuantityInputBlur}
                  style={dynamicStyles.quantityInput}
                  keyboardType="number-pad" // Use number-pad for easier numeric entry
                  mode="outlined" // Or "flat" for more compactness
                  dense // Makes the TextInput more compact
                  textAlign="center"
                  outlineColor={theme.colors.outline}
                  activeOutlineColor={theme.colors.primary}
                  selectTextOnFocus
                />
                <IconButton
                  icon="plus"
                  size={20} // Slightly larger for easier tap
                  onPress={incrementQuantity}
                  mode="outlined"
                  style={dynamicStyles.quantityButton}
                  iconColor={theme.colors.onSurface}
                />
              </View>
            </View>
            
            {/* Price and Amount in a row */}
            <View style={dynamicStyles.priceAmountRow}>
              <TextInput
                label="Price"
                value={item.price === 0 ? '' : String(item.price)}
                onChangeText={handlePriceChange}
                placeholder="0.00"
                placeholderTextColor={theme.colors.onSurfaceDisabled}
                left={<TextInput.Affix text={currencySymbol} />}
                style={[dynamicStyles.input, dynamicStyles.priceInput]}
                keyboardType="numeric"
                mode="outlined"
                outlineColor={theme.colors.outline}
                activeOutlineColor={theme.colors.primary}
              />
              
              <Surface style={dynamicStyles.amountContainer} elevation={0}>
                <Text style={dynamicStyles.amountTextLabel}>Amount</Text>
                <Text style={dynamicStyles.amountText}>
                  {currencySymbol}
                  {item.amount.toFixed(2)}
                </Text>
              </Surface>
            </View>
          </View>
        </View>
      </Surface>
    </View>
  );
};

const styles = (theme: ReactNativePaperTheme) => StyleSheet.create({
  itemContainer: {
    marginBottom: 16,
  },
  itemSurface: {
    borderRadius: 16,
    backgroundColor: theme.colors.surface,
    overflow: 'hidden',
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 8,
  },
  itemIndexText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary,
  },
  itemDetails: {
    padding: 16,
    paddingTop: 0,
  },
  row: {
    flexDirection: 'column', // Changed to column for better layout with the quantity selector
    marginTop: 8,
  },
  input: {
    backgroundColor: 'transparent',
  },
  itemNameInput: {
    marginBottom: 8,
  },
  // New quantity selector styles
  quantityContainer: {
    width: '100%',
    marginBottom: 12,
  },
  fieldLabel: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    marginBottom: 4, // Add some space below label
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between', // Distribute space for a more balanced look if needed
    width: '100%', // Ensure controls take full width of their container
  },
  quantityButton: {
    marginHorizontal: 0, // Reduce horizontal margin if using justifyContent: 'space-between'
    borderRadius: theme.roundness * 2, // Consistent rounding
  },
  quantityInput: { // Style for the new TextInput
    flex: 1, // Allow it to take available space between buttons
    textAlign: 'center',
    fontSize: 16,
    marginHorizontal: 8, // Space between buttons and input
    // backgroundColor: theme.colors.surfaceVariant, // Optional: different background
    // borderRadius: theme.roundness, // Optional: rounding
    maxHeight: 48, // Control height for compactness
    minWidth: 60, // Ensure it doesn't get too small
  },
  priceInput: {
    width: '48%',
    marginRight: '2%',
  },
  amountContainer: {
    width: '48%',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    backgroundColor: theme.colors.primaryContainer,
    borderRadius: 12,
    paddingHorizontal: 8,
    minHeight: 56,
    marginTop: 8,
  },
  amountTextLabel: {
    fontSize: 12,
    color: theme.colors.onPrimaryContainer,
    opacity: 0.8,
    marginBottom: 2,
  },
  amountText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.onPrimaryContainer,
  },
  deleteButton: {
    margin: 0,
    borderColor: theme.colors.errorContainer,
  },
  priceAmountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8, // Add some top margin if quantity selector is above
  },
});

export default React.memo(BillItemRow); 