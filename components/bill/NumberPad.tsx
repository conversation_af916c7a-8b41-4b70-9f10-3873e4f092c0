import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface NumberPadProps {
  onNumberPress: (num: string) => void;
  onBackspacePress: () => void;
  onDecimalPress: () => void;
  onClearPress: () => void;
  onEnterPress: () => void;
  onSavePress: () => void;
}

const NumberPad: React.FC<NumberPadProps> = ({
  onNumberPress,
  onBackspacePress,
  onDecimalPress,
  onClearPress,
  onEnterPress,
  onSavePress,
}) => {
  const theme = useTheme();
  
  const renderButton = (content: string | React.ReactNode, onPress: () => void, buttonStyle?: any, textStyle?: any) => (
    <TouchableOpacity
      style={[styles.button, buttonStyle]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {typeof content === 'string' ? <Text style={[styles.buttonText, textStyle]}>{content}</Text> : content}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Main grid for numbers and some actions */}
      <View style={styles.mainGrid}>
        {/* Column 1 */}
        <View style={styles.column}>
          {renderButton('7', () => onNumberPress('7'), styles.numberButton, styles.numberText)}
          {renderButton('4', () => onNumberPress('4'), styles.numberButton, styles.numberText)}
          {renderButton('1', () => onNumberPress('1'), styles.numberButton, styles.numberText)}
          {renderButton('0', () => onNumberPress('0'), styles.numberButton, styles.numberText)}
        </View>

        {/* Column 2 */}
        <View style={styles.column}>
          {renderButton('8', () => onNumberPress('8'), styles.numberButton, styles.numberText)}
          {renderButton('5', () => onNumberPress('5'), styles.numberButton, styles.numberText)}
          {renderButton('2', () => onNumberPress('2'), styles.numberButton, styles.numberText)}
          {renderButton('.', onDecimalPress, styles.numberButton, styles.numberText)}
        </View>

        {/* Column 3 */}
        <View style={styles.column}>
          {renderButton('9', () => onNumberPress('9'), styles.numberButton, styles.numberText)}
          {renderButton('6', () => onNumberPress('6'), styles.numberButton, styles.numberText)}
          {renderButton('3', () => onNumberPress('3'), styles.numberButton, styles.numberText)}
          {renderButton(
            <MaterialCommunityIcons name="backspace-outline" size={28} color="#fff" />,
            onBackspacePress,
            styles.numberButton
          )}
        </View>
      </View>

      {/* Side grid for Save, Enter, Clear */}
      <View style={styles.sideGrid}>
        {renderButton(
            <View style={styles.actionIconContainer}>
              <MaterialCommunityIcons name="content-save" size={24} color={theme.colors.onPrimary} />
              <Text style={[styles.actionTextSmall, { color: theme.colors.onPrimary }]}>Save</Text>
            </View>,
            onSavePress,
            [styles.sideButton, { backgroundColor: theme.colors.primary }]
        )}
        {renderButton(
            <View style={styles.enterButtonContent}>
              <Text style={[styles.enterText, { color: theme.colors.onTertiaryContainer }]}>Enter</Text>
              <Text style={[styles.xText, { color: theme.colors.onTertiaryContainer }]}>X</Text>
            </View>,
            onEnterPress,
            [styles.sideButton, styles.enterButton, { backgroundColor: theme.colors.tertiaryContainer }]
        )}
        {renderButton(
            <View style={styles.actionIconContainer}>
              <Text style={[styles.clearText, { color: theme.colors.onErrorContainer }]}>C</Text>
              <Text style={[styles.actionTextSmall, { color: theme.colors.onErrorContainer }]}>Clear</Text>
            </View>,
            onClearPress,
            [styles.sideButton, { backgroundColor: theme.colors.errorContainer }]
        )}
      </View>
    </View>
  );
};

const BUTTON_SIZE = 70;
const GRID_GAP = 8; // Gap between buttons and columns/rows

const styles = StyleSheet.create({
  container: {
    width: '100%',
    padding: GRID_GAP,
    backgroundColor: '#1c1c1c',
    flexDirection: 'row', // Arrange mainGrid and sideGrid horizontally
  },
  mainGrid: {
    flex: 3, // Takes 3/4 of the space
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  sideGrid: {
    flex: 1, // Takes 1/4 of the space
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginLeft: GRID_GAP,
  },
  column: {
    flexDirection: 'column',
    justifyContent: 'space-around',
  },
  button: {
    width: BUTTON_SIZE,
    height: BUTTON_SIZE,
    borderRadius: BUTTON_SIZE / 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: GRID_GAP / 2, // Vertical margin for buttons within columns
  },
  numberButton: {
    backgroundColor: '#333333',
  },
  buttonText: {
    fontWeight: 'bold',
  },
  numberText: {
    fontSize: 28,
    color: '#ffffff',
  },
  actionIconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  sideButton: {
    width: '100%', // Take full width of the side column
    height: BUTTON_SIZE,
    borderRadius: 35, // Keep consistent rounding
  },
  enterButton: {
    height: BUTTON_SIZE * 2 + GRID_GAP, // Span two button heights plus gap
  },
  enterButtonContent: {
    alignItems: 'center',
  },
  enterText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  xText: {
    fontSize: 26,
    fontWeight: 'bold',
  },
  clearText: {
    fontSize: 28,
    fontWeight: 'bold'
  },
  actionTextSmall: {
    fontSize: 12,
    marginTop: 2,
  },
});

export default NumberPad;