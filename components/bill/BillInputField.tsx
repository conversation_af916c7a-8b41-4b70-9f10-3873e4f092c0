import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface BillInputFieldProps {
  label: string;
  value: string;
  onPress: () => void;
  isActive?: boolean;
  icon?: keyof typeof MaterialCommunityIcons.glyphMap;
}

const BillInputField: React.FC<BillInputFieldProps> = ({ 
  label, 
  value, 
  onPress,
  isActive = false,
  icon
}) => {
  const theme = useTheme();
  
  return (
    <TouchableOpacity 
      style={[
        styles.container, 
        isActive ? { borderColor: theme.colors.tertiary, borderWidth: 2 } : { borderColor: '#444444', borderWidth: 1 }
      ]} 
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.contentContainer}>
        {icon && <MaterialCommunityIcons name={icon} size={22} color={isActive ? theme.colors.tertiary : '#999999'} style={styles.iconStyle} />}
        <View style={styles.textContainer}>
          <Text style={styles.label}>{label}</Text>
          <Text style={styles.value}>{value || '0'}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 10,
    backgroundColor: '#333333',
    minHeight: 60,
    justifyContent: 'center',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconStyle: {
    marginRight: 8,
  },
  textContainer: {
    flex: 1, // Ensure text takes remaining space if icon is present
  },
  label: {
    fontSize: 16,
    color: '#999999',
    marginBottom: 4,
  },
  value: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
  },
});

export default BillInputField; 