import React, { useEffect, useState, useCallback, useMemo, memo } from 'react';
import { View, Text, FlatList, StyleSheet, TouchableOpacity } from 'react-native';
import { ActivityIndicator, Card, Button } from 'react-native-paper';
import { useDatabase } from '@/components/DatabaseProvider';
import { Q } from '@nozbe/watermelondb';
import { format } from 'date-fns';

// Memoized Bill item to prevent unnecessary re-renders
const BillItem = memo(({ item, onPress }) => {
  // Format the date
  const billDate = new Date(item.date);
  const formattedDate = format(billDate, 'MMM dd, yyyy');
  
  return (
    <Card style={styles.card} mode="outlined">
      <Card.Content>
        <View style={styles.billHeader}>
          <Text style={styles.billNumber}>#{item.billNumber}</Text>
          <Text style={[
            styles.status, 
            item.status === 'paid' ? styles.paidStatus : styles.pendingStatus
          ]}>
            {item.status.toUpperCase()}
          </Text>
        </View>
        
        <Text style={styles.date}>{formattedDate}</Text>
        <Text style={styles.amount}>₹{item.totalAmount.toFixed(2)}</Text>
      </Card.Content>
      <Card.Actions>
        <Button onPress={() => onPress(item.id)}>
          View Details
        </Button>
      </Card.Actions>
    </Card>
  );
});

export default function BillsList({ navigation, filter = 'all' }) {
  const { database } = useDatabase();
  const [bills, setBills] = useState([]);
  const [loading, setLoading] = useState(true);

  // Extract query logic into a separate function
  const getBillsQuery = useCallback((filter) => {
    const billsCollection = database.get('bills');
    
    switch (filter) {
      case 'pending':
        return billsCollection.query(Q.where('status', 'pending'));
      case 'paid':
        return billsCollection.query(Q.where('status', 'paid'));
      case 'recent':
        // Get bills from the last 7 days
        const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
        return billsCollection.query(
          Q.where('date', Q.gte(sevenDaysAgo)),
          Q.sortBy('date', 'desc')
        );
      default:
        // All bills, sorted by date descending
        return billsCollection.query(Q.sortBy('date', 'desc'));
    }
  }, [database]);

  // Load bills when filter changes
  useEffect(() => {
    const billsCollection = database.get('bills');
    let subscription;
    
    const loadBills = async () => {
      try {
        const query = getBillsQuery(filter);
        
        // Subscribe to changes
        subscription = query.observe().subscribe(billsData => {
          setBills(billsData);
          setLoading(false);
        });
      } catch (error) {
        console.error('Error loading bills:', error);
        setLoading(false);
      }
    };
    
    loadBills();
    
    // Clean up subscription when component unmounts or filter changes
    return () => subscription?.unsubscribe();
  }, [database, filter, getBillsQuery]);
  
  // Handle bill selection with useCallback to prevent unnecessary re-renders
  const handleBillPress = useCallback((billId) => {
    navigation.navigate('BillDetails', { billId });
  }, [navigation]);
  
  // Memoize renderItem to prevent recreation on each render
  const renderBillItem = useCallback(({ item }) => {
    return <BillItem item={item} onPress={handleBillPress} />;
  }, [handleBillPress]);
  
  // Optimize FlatList with keyExtractor and getItemLayout
  const keyExtractor = useCallback((item) => item.id, []);
  
  // Calculate item height for better FlatList performance
  const getItemLayout = useCallback((data, index) => ({
    length: 150, // Approximate height of a bill card
    offset: 150 * index,
    index,
  }), []);
  
  // Loading state
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading bills...</Text>
      </View>
    );
  }
  
  // Empty state
  if (bills.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No bills found</Text>
        <Button mode="contained" onPress={() => navigation.navigate('NewBill')}>
          Create New Bill
        </Button>
      </View>
    );
  }
  
  // Render the list
  return (
    <FlatList
      data={bills}
      keyExtractor={keyExtractor}
      renderItem={renderBillItem}
      getItemLayout={getItemLayout}
      initialNumToRender={10}
      maxToRenderPerBatch={10}
      windowSize={5}
      removeClippedSubviews={true}
      contentContainerStyle={styles.listContainer}
    />
  );
}

const styles = StyleSheet.create({
  listContainer: {
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  billHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  billNumber: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  status: {
    fontSize: 12,
    fontWeight: 'bold',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  paidStatus: {
    backgroundColor: '#e6f7e6',
    color: '#2e7d32',
  },
  pendingStatus: {
    backgroundColor: '#fff8e1',
    color: '#ff8f00',
  },
  date: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  amount: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    fontSize: 18,
    marginBottom: 16,
    color: '#666',
  },
}); 