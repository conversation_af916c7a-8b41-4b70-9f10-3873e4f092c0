import React from 'react';
import { View, StyleSheet } from 'react-native';
import { <PERSON>ton, Card, Surface, Text, ActivityIndicator, useTheme, type MD3Theme as ReactNativePaperTheme } from 'react-native-paper';
import BillItemRow from './BillItemRow';
import { BillItem } from '@/types/bill';
// import Animated, { Layout } from 'react-native-reanimated'; // Comment out if Animated or Layout not used
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface BillItemsListProps {
  items: BillItem[];
  showItemNameInput: boolean;
  onUpdateItem: (index: number, field: keyof BillItem, value: string | number) => void;
  onRemoveItem: (index: number) => void;
  onAddItem: () => void;
  isLoadingSettings: boolean; 
  currencySymbol?: string;
}

// const AnimatedSurface = Animated.createAnimatedComponent(Surface); // Comment out or remove

const BillItemsList: React.FC<BillItemsListProps> = ({
  items,
  showItemNameInput,
  onUpdateItem,
  onRemoveItem,
  onAddItem,
  isLoadingSettings,
  currencySymbol = '₹',
}) => {
  const theme = useTheme();
  const dynamicStyles = styles(theme);

  if (isLoadingSettings) {
    return (
      <Surface style={dynamicStyles.loadingContainer} elevation={0}>
        <ActivityIndicator animating={true} size="large" color={theme.colors.primary} />
        <Text style={dynamicStyles.loaderText}>Loading item settings...</Text>
      </Surface>
    );
  }

  return (
    <View style={dynamicStyles.container}>
      <View style={dynamicStyles.sectionHeader}>
        <View style={dynamicStyles.sectionTitleContainer}>
          <MaterialCommunityIcons 
            name="cart-outline" 
            size={24} 
            color={theme.colors.primary} 
            style={dynamicStyles.sectionIcon} 
          />
          <Text style={dynamicStyles.sectionTitle}>Bill Items</Text>
        </View>
      </View>

      <Surface style={dynamicStyles.itemsContainer} elevation={0} /* layout={Layout.springify()} Removed layout animation */>
        {items.map((item, index) => (
          <BillItemRow
            key={index.toString()}
            item={item}
            index={index}
            showItemNameInput={showItemNameInput}
            onUpdateItem={onUpdateItem}
            onRemoveItem={onRemoveItem}
            currencySymbol={currencySymbol}
          />
        ))}
        
        <Button
          icon="plus-circle-outline"
          mode="outlined"
          onPress={onAddItem}
          style={dynamicStyles.addItemButton}
          labelStyle={dynamicStyles.addItemButtonLabel}
          buttonColor={theme.colors.surface}
          textColor={theme.colors.primary}
        >
          Add Item
        </Button>
      </Surface>
    </View>
  );
};

const styles = (theme: ReactNativePaperTheme) => StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  sectionHeader: {
    marginBottom: 16,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.onSurface,
  },
  sectionIcon: {
    marginRight: 8,
  },
  itemsContainer: {
    borderRadius: 0,
    backgroundColor: 'transparent',
  },
  addItemButton: {
    marginTop: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    borderStyle: 'dashed',
    marginHorizontal: 16,
  },
  addItemButtonLabel: {
    fontSize: 16,
    paddingVertical: 4,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 28,
    marginVertical: 20,
    borderRadius: 16,
    backgroundColor: theme.colors.surface,
  },
  loaderText: {
    marginTop: 12,
    color: theme.colors.onSurfaceVariant,
  },
});

export default BillItemsList; 