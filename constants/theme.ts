import { MD3DarkTheme, MD3LightTheme, MD3Theme } from 'react-native-paper';

// Modern color palette
const lightColors = {
  primary: '#6A3DE8', // Rich purple
  primaryContainer: '#EDE8FF', // Light purple background
  secondary: '#FF7A00', // Vibrant orange
  secondaryContainer: '#FFF2E6', // Light orange background
  background: '#F8F9FC', // Clean, slightly off-white background
  surface: '#FFFFFF',
  surfaceVariant: '#F2F4F7',
  error: '#D83333', // Vibrant red for errors
  errorContainer: '#FFDADA',
  onError: '#FFFFFF',
  onErrorContainer: '#621B1B',
  success: '#32A467', // Green for success states
  onPrimary: '#FFFFFF',
  onPrimaryContainer: '#22184D',
  onSecondary: '#FFFFFF',
  onSecondaryContainer: '#4D2600',
  onBackground: '#1B1B1F',
  onSurface: '#1B1B1F',
  onSurfaceVariant: '#636366',
  outlineVariant: '#E4E4E7',
  outline: '#DADADA',
  elevation: {
    level0: 'transparent',
    level1: '#F8F9FC',
    level2: '#F2F4F7',
    level3: '#EAECF0',
    level4: '#E4E7EC',
    level5: '#D8DCE3',
  },
};

const darkColors = {
  primary: '#8B5CF6', // Lighter purple for dark mode
  primaryContainer: '#4B3B72', // Darker purple container
  secondary: '#FF9937', // Lighter orange for dark mode
  secondaryContainer: '#663000', // Dark orange container
  background: '#121214', // Dark background
  surface: '#1E1E22',
  surfaceVariant: '#2C2C30',
  error: '#FF5252', // Bright red for errors
  errorContainer: '#4D1515',
  onError: '#FFFFFF',
  onErrorContainer: '#FFCCCC',
  success: '#4CAF50', // Green for success states
  onPrimary: '#FFFFFF',
  onPrimaryContainer: '#E9D8FF',
  onSecondary: '#FFFFFF',
  onSecondaryContainer: '#FFE0C2',
  onBackground: '#E9E9EB',
  onSurface: '#E9E9EB',
  onSurfaceVariant: '#A7A7AC',
  outlineVariant: '#3A3A3C',
  outline: '#525257',
  elevation: {
    level0: 'transparent',
    level1: '#1E1E22',
    level2: '#2C2C30',
    level3: '#36363A',
    level4: '#45454B',
    level5: '#52525A',
  },
};

export const CustomLightTheme: MD3Theme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    ...lightColors,
  },
  roundness: 10,
};

export const CustomDarkTheme: MD3Theme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    ...darkColors,
  },
  roundness: 10,
}; 