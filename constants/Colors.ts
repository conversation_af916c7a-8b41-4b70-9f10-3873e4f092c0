/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = '#007AFF';
const tintColorDark = '#0A84FF';

export const Colors = {
  light: {
    text: '#1C1C1E',
    background: '#F2F2F7',
    tint: tintColorLight,
    icon: '#8A8A8E',
    tabIconDefault: '#8A8A8E',
    tabIconSelected: tintColorLight,
    card: '#FFFFFF',
    border: '#D1D1D6',
  },
  dark: {
    text: '#FFFFFF',
    background: '#121212',
    tint: tintColorDark,
    icon: '#8D8D93',
    tabIconDefault: '#8D8D93',
    tabIconSelected: tintColorDark,
    card: '#1C1C1E',
    border: '#3A3A3C',
  },
};
