"use strict";

var _reactNative = require("react-native");
var _NativeModules = _reactNative.NativeModules;
var _DatabasePlatform = require("../../utils/common/DatabasePlatform");
var _common = require("../../utils/common");
var isDebuggingRemotely = _reactNative.DevSettings?.isDebuggingRemotely;

function _initializerDefineProperty(target, property, descriptor, context) { if (

function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

var isJSI = !isDebuggingRemotely;

try {
  isJSI = !!global.nativeWatermelonCreateAdapter;
} catch (e) {
  // Ignore
} 