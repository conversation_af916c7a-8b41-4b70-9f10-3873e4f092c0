diff --git a/node_modules/@nozbe/watermelondb/adapters/sqlite/index.js b/node_modules/@nozbe/watermelondb/adapters/sqlite/index.js
index 0000000..1000000 100644
--- a/node_modules/@nozbe/watermelondb/adapters/sqlite/index.js
+++ b/node_modules/@nozbe/watermelondb/adapters/sqlite/index.js
@@ -1,7 +1,9 @@
 "use strict";
 
-var _NativeModules = require("react-native").NativeModules;
+var _reactNative = require("react-native");
+var _NativeModules = _reactNative.NativeModules;
 var _DatabasePlatform = require("../../utils/common/DatabasePlatform");
+var isDebuggingRemotely = _reactNative.DevSettings?.isDebuggingRemotely;
 
 // Check if JSI adapter is available
-var isJSI = false;
+var isJSI = !isDebuggingRemotely;
